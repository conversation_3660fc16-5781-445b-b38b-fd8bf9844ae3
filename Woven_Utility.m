classdef Woven_Utility
    % Woven_Utility 提供各种用于Woven算法的实用函数
    % 包含计算距离、角度、多样性等各种指标的静态方法
    
    methods(Static)
        function distance = calculateCrowdingDistance(obj, exclude_idx, PopObj)
            % 计算经典的拥挤度距离
            % obj: 待评估解的目标函数值
            % exclude_idx: 待评估解在种群中的索引
            % PopObj: 整个种群的目标函数值矩阵
            [N, M] = size(PopObj);
            if N == 1
                distance = inf;
                return;
            end
            temp_pop = PopObj;
            temp_pop(exclude_idx, :) = obj;
            distance = 0;
            % 对每个目标函数
            for i = 1:M
                [sorted_obj, sorted_idx] = sort(temp_pop(:, i));
                eval_pos = find(sorted_idx == exclude_idx);
                if eval_pos == 1 || eval_pos == N
                    distance = inf;
                    return;
                end
                % 计算中间点的拥挤度
                % 目标值范围（最大值-最小值）
                obj_range = sorted_obj(N) - sorted_obj(1);
                if obj_range == 0
                    continue;
                end
                norm_dist = (sorted_obj(eval_pos+1) - sorted_obj(eval_pos-1)) / obj_range;
                distance = distance + norm_dist;
            end
        end
        
        function dist = calculateMEDDistance(obj_a, index, PopObj)
            % 计算MED距离
            % obj_a: 待评估解的目标函数值
            [N, M] = size(PopObj);
            dominated_count = 0;
            dominated_distance = 0;
            for i = 1:N
            if i == index
                continue;
            end
            better_or_equal = true;
            at_least_one_better = false;
            for j = 1:M
                if obj_a(j) > PopObj(i,j)
                better_or_equal = false;
                break;
                elseif obj_a(j) < PopObj(i,j)
                at_least_one_better = true;
                end
            end
            if better_or_equal && at_least_one_better
                dominated_count = dominated_count + 1;
                dominated_distance = dominated_distance + sqrt(sum((PopObj(i,:) - obj_a).^2));
            end
            end
            base_score = 0;
            if dominated_count > 0
            base_score = dominated_distance / dominated_count * (1 + log(dominated_count + 1));
            end
            dist = base_score;
        end
        
        function angle_score = Stage2AngleScore(obj_i, index, PopObj)
            % 第二阶段专用：计算解与其他解之间的角度评分
            % obj_i: 当前解的目标函数值
            % index: 当前解在种群中的索引
            % PopObj: 整个种群的目标函数值矩阵
            
            [N, ~] = size(PopObj);
            
            % 计算所有解的理想点
            ideal_point = min(PopObj);
            
            % 计算当前解到理想点的向量
            vec_i = obj_i - ideal_point;
            vec_i_norm = norm(vec_i);
            
            if vec_i_norm <= 0
                % 如果向量为零，返回一个小的默认值
                angle_score = 0.001;
                return;
            end
            
            vec_i = vec_i / vec_i_norm;
            
            % 计算所有其他解的单位向量
            angles = zeros(N-1, 1);
            count = 0;
            
            for j = 1:N
                if j == index
                    continue;
                end
                
                vec_j = PopObj(j,:) - ideal_point;
                vec_j_norm = norm(vec_j);
                
                if vec_j_norm > 0
                    count = count + 1;
                    vec_j = vec_j / vec_j_norm;
                    
                    % 计算夹角的余弦值
                    cos_angle = max(-1, min(1, vec_i * vec_j'));
                    angles(count) = acos(cos_angle);
                end
            end
            
            % 如果有有效的角度
            if count > 0
                angles = angles(1:count);
                
                % 计算平均角度和标准差
                mean_angle = mean(angles);
                std_angle = std(angles);
                
                % 角度评分: 平均角度越大且分布越均匀越好
                std_weight = 0.2;
                angle_score = mean_angle - std_weight * std_angle;
            else
                % 如果没有有效角度，返回一个小的默认值
                angle_score = 0.001;
            end
        end
        
        function diversity_score = Stage2DecisionSpaceDiversity(dec_i, index, PopDec)
            % 第二阶段专用：计算决策空间的多样性得分
            % dec_i: 当前解的决策变量
            % index: 当前解在种群中的索引
            % PopDec: 整个种群的决策变量矩阵
            
            [N, D] = size(PopDec);
            
            % 计算当前解与所有其他解的欧式距离
            distances = zeros(N-1, 1);
            count = 0;
            
            for j = 1:N
                if j == index
                    continue;
                end
                
                count = count + 1;
                % 计算欧式距离
                distances(count) = sqrt(sum((dec_i - PopDec(j,:)).^2));
            end
            
            % 如果没有其他解，返回一个小的默认值
            if count == 0
                diversity_score = 0.001;
                return;
            end
            
            % 提取所有计算的距离
            distances = distances(1:count);
            
            % 计算到最近的k个解的平均距离
            k = min(5, count);  % 选择最近的k个解，如果总数少于k则全部使用
            
            % 对距离进行排序
            sorted_distances = sort(distances);
            
            % 计算到最近k个解的平均距离
            nearest_k_distance = mean(sorted_distances(1:k));
            
            % 计算决策空间多样性得分
            % 距离越大意味着多样性越好
            diversity_score = nearest_k_distance;
            
            % 为避免极端值，可以使用一个非线性变换
            diversity_score = 1 - exp(-diversity_score);
        end
        
        function dom_result = checkDominance(obj_a, obj_b)
            % 检查支配关系，返回逻辑值: 1表示a支配b，-1表示b支配a，0表示互不支配
            better_or_equal = obj_a <= obj_b;
            strictly_better = obj_a < obj_b;
            
            if all(better_or_equal) && any(strictly_better)
                dom_result = 1;  % a支配b
            elseif all(obj_b <= obj_a) && any(obj_b < obj_a)
                dom_result = -1;  % b支配a
            else
                dom_result = 0;  % 互不支配
            end
        end
        
        function obj_flags = calculateStage1NonDominatedSet(PopObj)
            [N, ~] = size(PopObj);
            obj_flags = ones(N, 1); 
            for i = 1:N
                if obj_flags(i) == 0 
                    continue;
                end
                obj_i = PopObj(i,:);
                for j = 1:N
                    if i == j || obj_flags(j) == 0
                        continue;
                    end
                    obj_j = PopObj(j,:);
                    dom_result = Woven_Utility.checkDominance(obj_j, obj_i);
                    if dom_result == 1  % j支配i
                        obj_flags(i) = 0;  % 标记i为被支配解
                        break;  % i被支配，无需继续检查
                    elseif dom_result == -1  % i支配j
                        obj_flags(j) = 0;  % 标记j为被支配解
                    end
                end
            end
        end
        
        function obj_flags = calculateStage23NonDominatedSet(PopObj, PopCon, ~)
            [N, ~] = size(PopObj);
            obj_flags = 9 * ones(N, 1);  % 9=不可行, 0=可行但被支配, 1=非支配
            for i = 1:N
                if sum(max(0, PopCon(i,:))) == 0
                    obj_flags(i) = 0;
                end
            end
            feasible_indices = find(obj_flags == 0);
            for i = feasible_indices'
                is_non_dominated = true;
                obj_i = PopObj(i,:);
                for j = feasible_indices'
                    if i == j
                        continue;
                    end
                    obj_j = PopObj(j,:);
                    dom_result = Woven_Utility.checkDominance(obj_j, obj_i);
                    if dom_result == 1  % j支配i
                        is_non_dominated = false;
                        break;
                    end
                end
                if is_non_dominated
                    obj_flags(i) = 1;  % 标记为非支配
                end
            end
        end
        function dominated_count = calculateDominatedByFirstLayerCount(varargin)
            % 计算解被第一前沿解支配的次数（支持两种调用方式）
            %
            % obj: 待评估解的目标函数值
            % PopObj: 整个种群的目标函数值矩阵
            % PopCon: 整个种群的约束违反值矩阵（完整版本）
            % first_layer_flags: 第一前沿解的标记
            % epsilon_values: ε约束值（完整版本）
            % 返回: dominated_count - 被第一前沿解支配的次数
            
            dominated_count = 0;
            
            if nargin == 3
                % 简化版本调用：obj, PopObj, first_layer_flags
                obj = varargin{1};
                PopObj = varargin{2};
                first_layer_flags = varargin{3};
                
                first_layer_indices = find(first_layer_flags == 1);
                
                for i = 1:length(first_layer_indices)
                    idx = first_layer_indices(i);
                    if Woven_Utility.checkDominance(PopObj(idx,:), obj) == 1
                        dominated_count = dominated_count + 1;
                    end
                end
                
            elseif nargin == 5
                % 完整版本调用：obj, PopObj, PopCon, first_layer_flags, epsilon_values
                obj = varargin{1};
                PopObj = varargin{2};
                PopCon = varargin{3};
                first_layer_flags = varargin{4};
                epsilon_values = varargin{5};
                
                % 获取第一前沿解索引
                first_layer_indices = find(first_layer_flags == 1);
                
                % 遍历所有第一前沿解，检查是否支配当前解
                for i = first_layer_indices'
                    % 检查是否支配当前解
                    if Woven_Utility.checkDominance(PopObj(i,:), obj) == 1
                        dominated_count = dominated_count + 1;
                    end
                end
            else
                error('参数数量错误');
            end
        end
        
        function second_layer_flags = calculateSecondLayerSolutions(PopObj, first_layer_flags)
            % 计算二层解：被非支配解支配但不被其他解支配的解
            % PopObj: 目标函数值矩阵
            % first_layer_flags: 一层解（非支配解）的标记，如果为空则会计算
            
            [N, ~] = size(PopObj);
            second_layer_flags = zeros(N, 1);
            
            % 获取一层解和非一层解索引
            first_layer_indices = find(first_layer_flags == 1);
            non_first_indices = find(first_layer_flags ~= 1);
            
            % 快速预筛选：检查哪些解被至少一个一层解支配
            potential_second_layer = false(N, 1);
            
            for i = non_first_indices'
                obj_i = PopObj(i,:);
                
                for j = first_layer_indices'
                    if Woven_Utility.checkDominance(PopObj(j,:), obj_i) == 1
                        potential_second_layer(i) = true;
                        break;
                    end
                end
            end
            
            potential_indices = find(potential_second_layer);
            
            for i = 1:length(potential_indices)
                idx = potential_indices(i);
                obj_i = PopObj(idx,:);
                is_second_layer = true;
                
                for j = non_first_indices'
                    if idx == j
                        continue;
                    end
                    
                    if Woven_Utility.checkDominance(PopObj(j,:), obj_i) == 1
                        is_second_layer = false;
                        break;
                    end
                end
                
                if is_second_layer
                    second_layer_flags(idx) = 1;
                end
            end
        end
        
        function lambda = calculateAdaptiveThreshold(PopObj, M, N)
            % 根据公式计算自适应阈值 λ = 10^(M-2) × Obj / (M × N)
            % PopObj: 目标函数值矩阵
            % M: 目标维度
            % N: 种群大小
            
            % 计算目标空间的平均值
            Obj = mean(max(PopObj) - min(PopObj));
            
            % 计算自适应阈值
            lambda = 10^(M-2) * Obj / (M * N);
        end
        
        function change = calculateObjectiveChange(current_obj, previous_obj)
            % 计算两代之间目标空间的变化
            % current_obj: 当前代的目标函数值
            % previous_obj: 上一代的目标函数值
            
            if isempty(previous_obj)
                change = inf;
                return;
            end
            
            % 计算当前代和上一代的理想点和最差点
            current_min = min(current_obj);
            current_max = max(current_obj);
            previous_min = min(previous_obj);
            previous_max = max(previous_obj);
            
            % 计算理想点和最差点的变化
            ideal_change = norm(current_min - previous_min);
            nadir_change = norm(current_max - previous_max);
            
            % 总变化为理想点和最差点变化的平均
            change = (ideal_change + nadir_change) / 2;
        end
        
        function epsilon_values = calculateEpsilonValues(PopCon, elapsed_fe, max_fe, constraint_beta)
            % 计算各个约束的ε值，从搜索开始就直接下降
            % PopCon: 约束违反值矩阵 (N×约束个数)
            % elapsed_fe: 已进行的评估次数
            % max_fe: 最大评估次数
            % constraint_beta: 约束收缩指数
            
            % 归一化进度
            progress = min(1, elapsed_fe / max_fe);
            
            % 获取约束数量
            [~, num_constraints] = size(PopCon);
            epsilon_values = zeros(1, num_constraints);
            
            % 计算当前种群中每个约束的违反情况
            for j = 1:num_constraints
                constraint_violations = max(0, PopCon(:, j));
                non_zero_violations = constraint_violations(constraint_violations > 0);
                
                if isempty(non_zero_violations)
                    epsilon_values(j) = 0; 
                else
                    max_violation = max(non_zero_violations);
                    mean_violation = mean(non_zero_violations);
                    
                    initial_epsilon = max_violation * 0.2;
                    
                    % 使用平滑的幂函数直接从初始值开始下降
                    cp = (1 - progress)^(1/constraint_beta);
                    
                    % 从初始值开始下降到0
                    epsilon_values(j) = initial_epsilon * cp;
                end
            end
        end
        
        function is_epsilon_feasible = checkEpsilonFeasibility(PopCon, epsilon_values)
            % 检查解是否满足ε约束
            % PopCon: 约束违反值矩阵 (N×约束个数) 或单个解的约束向量
            % epsilon_values: 各约束的ε值
            
            % 确保PopCon是二维的，即使只有一行
            if isvector(PopCon) && size(PopCon, 1) == 1
                % 如果是行向量，保持原样
            elseif isvector(PopCon)
                % 如果是列向量，转置为行向量
                PopCon = PopCon';
            end
            
            [popSize, num_constraints] = size(PopCon);
            is_epsilon_feasible = true(popSize, 1);
            
            for i = 1:popSize
                for j = 1:min(num_constraints, length(epsilon_values))
                    if PopCon(i, j) > epsilon_values(j)
                        is_epsilon_feasible(i) = false;
                        break;
                    end
                end
            end
        end
        
        function new_better = Stage3CompareAngleScores(PopObj, obj_flags, old_obj, new_obj, idx)
            % 第三阶段专用：比较两个解的角度评分
            % PopObj: 种群目标函数值矩阵
            % obj_flags: 非支配解标记
            % old_obj, new_obj: 父代和子代的目标函数值
            % idx: 当前个体在种群中的索引
            % 返回: new_better - true表示子代更好，false表示父代更好
            
            new_better = false;
            
            non_dominated_indices = find(obj_flags == 1);
            if length(non_dominated_indices) > 1
                ideal_point = min(PopObj);
                % 计算候选解到理想点的向量
                vec_old = old_obj - ideal_point;
                vec_new = new_obj - ideal_point;
                % 归一化向量
                vec_old_norm = norm(vec_old);
                vec_new_norm = norm(vec_new);
                
                if (vec_old_norm > 0 && vec_new_norm > 0)
                    vec_old = vec_old / vec_old_norm;
                    vec_new = vec_new / vec_new_norm;
                    
                    % 计算所有非支配解的单位向量
                    all_vecs = zeros(length(non_dominated_indices), size(PopObj, 2));
                    valid_count = 0;
                    for k = 1:length(non_dominated_indices)
                        j = non_dominated_indices(k);
                        if j == idx
                            continue;
                        end
                        vec_other = PopObj(j,:) - ideal_point;
                        vec_other_norm = norm(vec_other);
                        if vec_other_norm > 0
                            valid_count = valid_count + 1;
                            all_vecs(valid_count,:) = vec_other / vec_other_norm;
                        end
                    end
                    
                    all_vecs = all_vecs(1:valid_count,:);
                    if valid_count > 0
                        % 计算角度
                        old_angles = zeros(valid_count, 1);
                        new_angles = zeros(valid_count, 1);
                        
                        for k = 1:valid_count
                            cos_angle_old = max(-1, min(1, vec_old * all_vecs(k,:)'));
                            cos_angle_new = max(-1, min(1, vec_new * all_vecs(k,:)'));
                            
                            old_angles(k) = acos(cos_angle_old);
                            new_angles(k) = acos(cos_angle_new);
                        end
                        
                        [~, M] = size(PopObj);
                        old_is_extreme = false;
                        new_is_extreme = false;
                        
                        % 对每个目标维度检查极值解
                        for m = 1:M
                            min_val = min(PopObj(:,m));
                            % 使用严格阈值
                            if abs(old_obj(m) - min_val) < 1e-6
                                old_is_extreme = true;
                            end
                            if abs(new_obj(m) - min_val) < 1e-6
                                new_is_extreme = true;
                            end
                        end
                        
                        % 极值解优先
                        if new_is_extreme && ~old_is_extreme
                            new_better = true;
                            return;
                        elseif old_is_extreme && ~new_is_extreme
                            new_better = false;
                            return;
                        end
                        
                        % 计算角度评分
                        old_max_angle = max(old_angles);
                        new_max_angle = max(new_angles);
                        old_min_angle = min(old_angles);
                        new_min_angle = min(new_angles);
                        old_score = old_max_angle * 0.7 + old_min_angle * 0.3;
                        new_score = new_max_angle * 0.7 + new_min_angle * 0.3;
                        new_better = (new_score > old_score);
                        end
                    end
                end
        end
                         
        function dominated_count = calculateDominatedByNonDominatedSet(obj, PopObj, non_dominated_flags)
            % 计算解被非支配解支配的次数（用于第一阶段选择）
            % obj: 待评估解的目标函数值
            % PopObj: 整个种群的目标函数值矩阵
            % non_dominated_flags: 非支配解的标记（1表示非支配解，0表示被支配解）
            % 返回: dominated_count - 被非支配解支配的次数

            dominated_count = 0;

            % 获取非支配解索引
            non_dominated_indices = find(non_dominated_flags == 1);

            % 遍历所有非支配解，检查是否支配当前解
            for i = non_dominated_indices'
                if Woven_Utility.checkDominance(PopObj(i,:), obj) == 1
                    dominated_count = dominated_count + 1;
                end
            end
        end

        function solution_types = classifyStage2Solutions(PopObj, PopCon, epsilon_values)
            % 第二阶段解分类函数 - 将解分为5类
            % PopObj: 目标函数值矩阵 (N×M)
            % PopCon: 约束违反值矩阵 (N×约束个数)
            % epsilon_values: 当前的ε约束值
            % 返回: solution_types - 解类型向量 (N×1)
            %   1: 第一前沿可行解
            %   2: 非第一前沿可行解
            %   3: 支配第一前沿可行解的在约束放松范围内的不可行解
            %   4: 被第一前沿可行解支配或和第一前沿可行解互不支配的在约束放松范围内的不可行解
            %   5: 不在约束范围内的不可行解

            [N, ~] = size(PopObj);
            solution_types = zeros(N, 1);

            % 步骤1：检查真实可行性
            feasible_flags = false(N, 1);
            for i = 1:N
                feasible_flags(i) = sum(max(0, PopCon(i,:))) <= 0;
            end

            % 步骤2：检查ε可行性
            epsilon_feasible_flags = Woven_Utility.checkEpsilonFeasibility(PopCon, epsilon_values);

            % 步骤3：计算第一前沿可行解
            feasible_indices = find(feasible_flags);
            first_front_feasible_flags = false(N, 1);

            if ~isempty(feasible_indices)
                % 只在可行解中计算第一前沿
                feasible_obj = PopObj(feasible_indices, :);
                feasible_first_front = Woven_Utility.calculateStage1NonDominatedSet(feasible_obj);

                % 将结果映射回原始索引
                for i = 1:length(feasible_indices)
                    if feasible_first_front(i) == 1
                        first_front_feasible_flags(feasible_indices(i)) = true;
                    end
                end
            end

            % 步骤4：分类
            for i = 1:N
                if feasible_flags(i)
                    % 真实可行解
                    if first_front_feasible_flags(i)
                        solution_types(i) = 1; % 第一前沿可行解
                    else
                        solution_types(i) = 2; % 非第一前沿可行解
                    end
                elseif epsilon_feasible_flags(i)
                    % ε可行但真实不可行
                    % 检查与第一前沿可行解的支配关系
                    dominates_first_front = false;
                    dominated_by_first_front = false;

                    first_front_indices = find(first_front_feasible_flags);
                    for j = first_front_indices'
                        dom_result = Woven_Utility.checkDominance(PopObj(i,:), PopObj(j,:));
                        if dom_result == 1
                            dominates_first_front = true;
                            break;
                        elseif dom_result == -1
                            dominated_by_first_front = true;
                        end
                    end

                    if dominates_first_front
                        solution_types(i) = 3; % 支配第一前沿可行解
                    else
                        % 被第一前沿可行解支配或互不支配，都归类为第4类
                        solution_types(i) = 4; % 被第一前沿可行解支配或互不支配
                    end
                else
                    % ε不可行
                    solution_types(i) = 5; % 不在约束范围内的不可行解
                end
            end
        end

        function solution_type = classifySingleStage2Solution(obj, con, PopObj, PopCon, epsilon_values, first_front_feasible_flags)
            % 对单个解进行第二阶段分类
            % obj: 单个解的目标函数值
            % con: 单个解的约束违反值
            % PopObj: 种群目标函数值矩阵
            % PopCon: 种群约束违反值矩阵
            % epsilon_values: 当前的ε约束值
            % first_front_feasible_flags: 第一前沿可行解标记
            % 返回: solution_type - 解类型 (1-5)

            % 检查真实可行性
            is_feasible = sum(max(0, con)) <= 0;

            % 检查ε可行性
            is_epsilon_feasible = Woven_Utility.checkEpsilonFeasibility(con, epsilon_values);

            if is_feasible
                % 真实可行解，需要检查是否在第一前沿
                % 这里需要临时计算，因为新解不在种群中
                first_front_indices = find(first_front_feasible_flags);
                is_first_front = true;

                for i = first_front_indices'
                    if Woven_Utility.checkDominance(PopObj(i,:), obj) == 1
                        is_first_front = false;
                        break;
                    end
                end

                if is_first_front
                    solution_type = 1; % 第一前沿可行解
                else
                    solution_type = 2; % 非第一前沿可行解
                end
            elseif is_epsilon_feasible
                % ε可行但真实不可行
                % 检查与第一前沿可行解的支配关系
                dominates_first_front = false;
                dominated_by_first_front = false;

                first_front_indices = find(first_front_feasible_flags);
                for i = first_front_indices'
                    dom_result = Woven_Utility.checkDominance(obj, PopObj(i,:));
                    if dom_result == 1
                        dominates_first_front = true;
                        break;
                    elseif dom_result == -1
                        dominated_by_first_front = true;
                    end
                end

                if dominates_first_front
                    solution_type = 3; % 支配第一前沿可行解
                else
                    % 被第一前沿可行解支配或互不支配，都归类为第4类
                    solution_type = 4; % 被第一前沿可行解支配或互不支配
                end
            else
                % ε不可行
                solution_type = 5; % 不在约束范围内的不可行解
            end
        end


    end
end
