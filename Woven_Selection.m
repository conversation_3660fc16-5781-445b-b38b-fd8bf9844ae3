classdef Woven_Selection < handle
    properties
        last_factors = [];     % 记录每个个体上次使用的选择因素
        obj_flags = [];        % 添加属性保存非支配标志
        record;                % 添加record属性
    end
    
    methods
        function obj = Woven_Selection(record)
            obj.record = record;
        end 
        function selected = Stage1Selection(obj, old_dec, old_obj, new_dec, new_obj, new_con, index, PopDec, PopObj)
            dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
            if dom_result == 1
                selected = true;
            elseif dom_result == 0
                % 计算当前种群的非支配解集合
                non_dominated_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);
                
                % 使用非支配解来计算被支配次数
                old_BeDomCT = Woven_Utility.calculateDominatedByNonDominatedSet(old_obj, PopObj, non_dominated_flags);
                
                new_BeDomCT = Woven_Utility.calculateDominatedByNonDominatedSet(new_obj, PopObj, non_dominated_flags);
                
                if new_BeDomCT < old_BeDomCT
                    selected = true;
                elseif new_BeDomCT == old_BeDomCT % 被支配次数相同 则比较MED值
                    old_med = Woven_Utility.calculateMEDDistance(old_obj, index, PopObj);
                    new_med = Woven_Utility.calculateMEDDistance(new_obj, index, PopObj);
                    selected = (new_med > old_med);
                else
                    selected = false;
                end
            else
                selected = false;
            end
        end
        function selected = Stage2Selection(obj, old_dec, old_obj, old_con, new_dec, new_obj, new_con, index, PopDec, PopObj, PopCon, epsilon_values, first_layer_flags)
                % 第二阶段选择函数 - 基于新的5类解分类系统
                % 输入参数:
                %   old_dec, old_obj, old_con: 父代的决策变量、目标函数值、约束违反值
                %   new_dec, new_obj, new_con: 子代的决策变量、目标函数值、约束违反值
                %   index: 当前个体在种群中的索引
                %   PopDec, PopObj, PopCon: 整个种群的决策变量、目标函数值、约束违反值
                %   epsilon_values: 当前的ε约束值
                %   first_layer_flags: 第一前沿解的标记

                % 计算第一前沿可行解标记（只包括真实可行解中的第一前沿）
                first_front_feasible_flags = false(size(PopObj, 1), 1);
                feasible_indices = [];
                for i = 1:size(PopCon, 1)
                    if sum(max(0, PopCon(i,:))) <= 0
                        feasible_indices = [feasible_indices; i];
                    end
                end

                if ~isempty(feasible_indices)
                    feasible_obj = PopObj(feasible_indices, :);
                    feasible_first_front = Woven_Utility.calculateStage1NonDominatedSet(feasible_obj);

                    for i = 1:length(feasible_indices)
                        if feasible_first_front(i) == 1
                            first_front_feasible_flags(feasible_indices(i)) = true;
                        end
                    end
                end

                % 确定父代的类型
                old_type = Woven_Utility.classifySingleStage2Solution(old_obj, old_con, PopObj, PopCon, epsilon_values, first_front_feasible_flags);

                % 确定子代的类型
                new_type = Woven_Utility.classifySingleStage2Solution(new_obj, new_con, PopObj, PopCon, epsilon_values, first_front_feasible_flags);

                % 使用新的环境选择逻辑
                selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_front_feasible_flags, epsilon_values, old_type, new_type);
        end
        
        function selected = Stage3Selection(obj, old_dec, old_obj, old_con, new_dec, new_obj, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, Problem)
            % 第三阶段选择函数
            % 输入参数:
            %   old_dec, old_obj, old_con: 父代的决策变量、目标函数值、约束违反值
            %   new_dec, new_obj, new_con: 子代的决策变量、目标函数值、约束违反值
            %   idx: 当前个体在种群中的索引
            %   PopDec, PopObj, PopCon: 整个种群的决策变量、目标函数值、约束违反值
            %   first_layer_flags: 第一前沿解的标记
            %   Problem: 问题实例，用于获取当前评估次数
            % 返回: selected - true表示子代更好，false表示父代更好
            
            % 检查真实可行性（第三阶段中，可行解包括真实可行解和范围内约束解）
            % 但在当前实现中，第三阶段只处理真实可行性
            parent_feasible = sum(max(0, old_con)) <= 0;
            child_feasible = sum(max(0, new_con)) <= 0;
            
            % 根据父代和子代的可行性进行分类处理，使用新的环境选择逻辑
            if parent_feasible && child_feasible
                % 情况1：可行解 → 可行解（使用第一阶段策略）
                selected = obj.Stage3EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, 'feasible_to_feasible', Problem);
                
            elseif parent_feasible && ~child_feasible
                % 情况2：可行解 → 不可行解
                selected = obj.Stage3EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, 'feasible_to_infeasible', Problem);
                
            elseif ~parent_feasible && child_feasible
                % 情况3：不可行解 → 可行解（使用第二阶段范围内约束解策略）
                selected = obj.Stage3EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, 'infeasible_to_feasible', Problem);
                
            else
                % 情况4：不可行解 → 不可行解（使用第二阶段范围内约束解策略）
                selected = obj.Stage3EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, 'infeasible_to_infeasible', Problem);
            end
        end

        function child_better = Stage2SelectionScore(obj, parent_dec, parent_obj, child_dec, child_obj, index, PopDec, PopObj)
            % Stage2中的综合评分比较（基于加权求和）
            % parent_dec, parent_obj: 父代的决策变量和目标函数值
            % child_dec, child_obj: 子代的决策变量和目标函数值
            % index: 解在种群中的索引
            % PopDec, PopObj: 整个种群的决策变量和目标函数值
            % 返回: child_better - true表示子代更好，false表示父代更好
            
            % 获取权重
            weights = obj.record.stage2_factor_weights;
            
            % 1. 计算角度评分并比较
            parent_angle_score = Woven_Utility.Stage2AngleScore(parent_obj, index, PopObj);
            child_angle_score = Woven_Utility.Stage2AngleScore(child_obj, index, PopObj);
            angle_factor = double(child_angle_score > parent_angle_score);
            
            % 2. 计算MED距离并比较
            parent_med_score = Woven_Utility.calculateMEDDistance(parent_obj, index, PopObj);
            child_med_score = Woven_Utility.calculateMEDDistance(child_obj, index, PopObj);
            med_factor = double(child_med_score > parent_med_score);
            
            % 3. 计算决策空间多样性并比较
            parent_diversity_score = Woven_Utility.Stage2DecisionSpaceDiversity(parent_dec, index, PopDec);
            child_diversity_score = Woven_Utility.Stage2DecisionSpaceDiversity(child_dec, index, PopDec);
            diversity_factor = double(child_diversity_score > parent_diversity_score);
            
            % 4. 计算加权总分
            parent_total_score = 0; % 父代作为基准，总分为0
            child_total_score = angle_factor * weights.angle + ...
                               med_factor * weights.med + ...
                               diversity_factor * weights.decision_space_diversity;
            
            % 5. 根据总分比较决定选择
            child_better = (child_total_score > parent_total_score);
            
            % 可选：输出选择结果用于调试（取消注释以启用）
            % fprintf('Stage2选择: 父代总分=%.4f, 子代总分=%.4f, 选择=%s\n', ...
            %     parent_total_score, child_total_score, ...
            %     char(ternary(child_better, '子代', '父代')));
        end

        function child_better = Stage3SelectionScore(obj, parent_dec, parent_obj, child_dec, child_obj, index, PopDec, PopObj)
            % Stage3中的综合评分比较（基于加权求和）
            % parent_dec, parent_obj: 父代的决策变量和目标函数值
            % child_dec, child_obj: 子代的决策变量和目标函数值
            % index: 解在种群中的索引
            % PopDec, PopObj: 整个种群的决策变量和目标函数值
            % 返回: child_better - true表示子代更好，false表示父代更好
            
            % 获取权重
            weights = obj.record.stage3_factor_weights;
            
            % 1. 计算角度评分并比较
            parent_angle_score = Woven_Utility.Stage2AngleScore(parent_obj, index, PopObj);
            child_angle_score = Woven_Utility.Stage2AngleScore(child_obj, index, PopObj);
            angle_factor = double(child_angle_score > parent_angle_score);
            
            % 2. 计算MED距离并比较
            parent_med_score = Woven_Utility.calculateMEDDistance(parent_obj, index, PopObj);
            child_med_score = Woven_Utility.calculateMEDDistance(child_obj, index, PopObj);
            med_factor = double(child_med_score > parent_med_score);
            
            % 3. 计算拥挤距离并比较
            parent_crowding_score = Woven_Utility.calculateCrowdingDistance(parent_obj, index, PopObj);
            temp_obj = PopObj(index,:);
            PopObj(index,:) = child_obj;
            child_crowding_score = Woven_Utility.calculateCrowdingDistance(child_obj, index, PopObj);
            PopObj(index,:) = temp_obj;
            diversity_factor = double(child_crowding_score > parent_crowding_score);
            
            % 4. 计算加权总分
            parent_total_score = 0; % 父代作为基准，总分为0
            child_total_score = angle_factor * weights.angle + ...
                               med_factor * weights.med + ...
                               diversity_factor * weights.diversity;
            
            % 5. 根据总分比较决定选择
            child_better = (child_total_score > parent_total_score);
            
            % 可选：输出选择结果用于调试（取消注释以启用）
            % fprintf('Stage3选择: 父代总分=%.4f, 子代总分=%.4f, 选择=%s\n', ...
            %     parent_total_score, child_total_score, ...
            %     char(ternary(child_better, '子代', '父代')));
        end
        function selected = Stage2EnvironmentSelection(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, old_type, new_type)
            % 第二阶段环境选择函数 - 基于新的5类解分类系统
            % 输入参数:
            %   old_obj, new_obj: 父代和子代的目标函数值
            %   old_dec, new_dec: 父代和子代的决策变量
            %   old_con, new_con: 父代和子代的约束违反值
            %   index: 当前个体在种群中的索引
            %   PopDec, PopObj, PopCon: 整个种群的决策变量、目标函数值、约束违反值
            %   first_layer_flags: 第一前沿解的标记（只包括真实可行解）
            %   epsilon_values: 当前的ε约束值
            %   old_type, new_type: 父代和子代的解类型 (1-5)
            % 返回: selected - true表示选择子代，false表示选择父代
            %
            % 解类型定义:
            %   1: 第一前沿可行解
            %   2: 非第一前沿可行解
            %   3: 支配第一前沿可行解的在约束放松范围内的不可行解
            %   4: 被第一前沿可行解支配的在约束放松范围内的不可行解
            %   5: 不在约束范围内的不可行解

            % 根据父代和子代的类型组合进行选择
            selection_key = old_type * 10 + new_type;

            switch selection_key
                % ========== 第一前沿可行解产生的个体 (old_type = 1) ==========
                case 11 % 1→1: 第一前沿可行解 → 第一前沿可行解
                    selected = obj.handleType1ToType1(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 12 % 1→2: 第一前沿可行解 → 非第一前沿可行解
                    selected = obj.handleType1ToType2(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 13 % 1→3: 第一前沿可行解 → 支配第一前沿的ε不可行解
                    selected = false; % 必须不违反约束，如果违反约束，则不接受新解

                case 14 % 1→4: 第一前沿可行解 → 被第一前沿支配的ε不可行解
                    selected = false; % 必须不违反约束，如果违反约束，则不接受新解

                case 15 % 1→5: 第一前沿可行解 → 完全不可行解
                    selected = false; % 必须不违反约束，如果违反约束，则不接受新解

                % ========== 非第一前沿可行解产生的个体 (old_type = 2) ==========
                case 21 % 2→1: 非第一前沿可行解 → 第一前沿可行解
                    selected = obj.handleType2ToType1(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values);

                case 22 % 2→2: 非第一前沿可行解 → 非第一前沿可行解
                    selected = obj.handleType2ToType2(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values);

                case 23 % 2→3: 非第一前沿可行解 → 支配第一前沿的ε不可行解
                    selected = false; % 必须不违反约束放松，如果违反约束放松，则不接受新解

                case 24 % 2→4: 非第一前沿可行解 → 被第一前沿支配的ε不可行解
                    selected = false; % 必须不违反约束放松，如果违反约束放松，则不接受新解

                case 25 % 2→5: 非第一前沿可行解 → 完全不可行解
                    selected = false; % 必须不违反约束放松，如果违反约束放松，则不接受新解

                % ========== 支配第一前沿的ε不可行解产生的个体 (old_type = 3) ==========
                case 31 % 3→1: 支配第一前沿的ε不可行解 → 第一前沿可行解
                    selected = obj.handleType3ToType1(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 32 % 3→2: 支配第一前沿的ε不可行解 → 非第一前沿可行解
                    selected = obj.handleType3ToType2(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 33 % 3→3: 支配第一前沿的ε不可行解 → 支配第一前沿的ε不可行解
                    selected = obj.handleType3ToType3(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 34 % 3→4: 支配第一前沿的ε不可行解 → 被第一前沿支配的ε不可行解
                    selected = obj.handleType3ToType4(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 35 % 3→5: 支配第一前沿的ε不可行解 → 完全不可行解
                    selected = obj.handleType3ToType5(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                % ========== 被第一前沿支配或互不支配的ε不可行解产生的个体 (old_type = 4) ==========
                case 41 % 4→1: 被第一前沿支配的ε不可行解 → 第一前沿可行解
                    selected = obj.handleType4ToType1(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values);

                case 42 % 4→2: 被第一前沿支配的ε不可行解 → 非第一前沿可行解
                    selected = obj.handleType4ToType2(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values);

                case 43 % 4→3: 被第一前沿支配的ε不可行解 → 支配第一前沿的ε不可行解
                    selected = obj.handleType4ToType3(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values);

                case 44 % 4→4: 被第一前沿支配的ε不可行解 → 被第一前沿支配的ε不可行解
                    selected = obj.handleType4ToType4(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values);

                case 45 % 4→5: 被第一前沿支配的ε不可行解 → 完全不可行解
                    selected = obj.handleType4ToType5(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values);

                % ========== 完全不可行解产生的个体 (old_type = 5) ==========
                case 51 % 5→1: 完全不可行解 → 第一前沿可行解
                    selected = obj.handleType5ToType1(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 52 % 5→2: 完全不可行解 → 非第一前沿可行解
                    selected = obj.handleType5ToType2(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 53 % 5→3: 完全不可行解 → 支配第一前沿的ε不可行解
                    selected = obj.handleType5ToType3(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 54 % 5→4: 完全不可行解 → 被第一前沿支配的ε不可行解
                    selected = obj.handleType5ToType4(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                case 55 % 5→5: 完全不可行解 → 完全不可行解
                    selected = obj.handleType5ToType5(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags);

                otherwise
                    error('Stage2EnvironmentSelection: 未知的选择情况 - %d', selection_key);
            end
        end

        % ========== 第一前沿可行解的处理函数 ==========
        function selected = handleType1ToType1(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 1→1: 第一前沿可行解 → 第一前沿可行解
            selected = obj.handleFeasibleToFeasible(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj);
        end

        function selected = handleType1ToType2(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 1→2: 第一前沿可行解 → 非第一前沿可行解
            selected = obj.handleFeasibleToFeasible(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj);
        end

        % ========== 通用处理函数 ==========
        function selected = handleFeasibleToFeasible(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj)
            % 可行解到可行解的通用处理逻辑
            % 规则：必须不违反约束，如果违反约束，则不接受新解；新解支配旧解 → 接受新解；互不支配 → 多样性比较；多样性没有旧解好或相等 → 拒绝新解

            % 检查新解是否违反约束
            if sum(max(0, new_con)) > 0
                selected = false;
                return;
            end

            dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
            if dom_result == 1
                % 新解支配旧解
                selected = true;
            elseif dom_result == 0
                % 互不支配，比较多样性
                selected = obj.Stage2SelectionScore(old_dec, old_obj, new_dec, new_obj, index, PopDec, PopObj);
            else
                % 旧解支配新解
                selected = false;
            end
        end

        % ========== 非第一前沿可行解的处理函数 ==========
        function selected = handleType2ToType1(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values)
            % 2→1: 非第一前沿可行解 → 第一前沿可行解
            selected = obj.handleEpsilonFeasibleSolution(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, first_layer_flags, epsilon_values);
        end

        function selected = handleType2ToType2(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values)
            % 2→2: 非第一前沿可行解 → 非第一前沿可行解
            selected = obj.handleEpsilonFeasibleSolution(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, first_layer_flags, epsilon_values);
        end

        function selected = handleEpsilonFeasibleSolution(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, first_layer_flags, epsilon_values)
            % ε可行解的通用处理逻辑
            % 规则：必须不违反约束放松，如果违反约束放松，则不接受新解；新解支配旧解 → 接受新解；互不支配 → 被前沿解支配次数；支配次数一样→检查多样性是否比旧解好；多样性没有旧解好或相等 → 拒绝新解

            % 检查新解是否违反约束放松
            if ~Woven_Utility.checkEpsilonFeasibility(new_con, epsilon_values)
                selected = false;
                return;
            end

            dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
            if dom_result == 1
                % 新解支配旧解
                selected = true;
            elseif dom_result == 0
                % 互不支配，比较被第一前沿解支配次数
                old_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(old_obj, PopObj, first_layer_flags);
                new_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(new_obj, PopObj, first_layer_flags);

                if new_dominated_count < old_dominated_count
                    % 被支配次数更少
                    selected = true;
                elseif new_dominated_count == old_dominated_count
                    % 被支配次数相等，比较多样性
                    selected = obj.Stage2SelectionScore(old_dec, old_obj, new_dec, new_obj, index, PopDec, PopObj);
                else
                    % 被支配次数更多
                    selected = false;
                end
            else
                % 旧解支配新解
                selected = false;
            end
        end

        % ========== 支配第一前沿的ε不可行解的处理函数 ==========
        function selected = handleType3ToType1(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 3→1: 支配第一前沿的ε不可行解 → 第一前沿可行解
            selected = obj.handleConstraintViolationComparison(old_obj, new_obj, old_con, new_con);
        end

        function selected = handleType3ToType2(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 3→2: 支配第一前沿的ε不可行解 → 非第一前沿可行解
            selected = obj.handleConstraintViolationComparison(old_obj, new_obj, old_con, new_con);
        end

        function selected = handleType3ToType3(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 3→3: 支配第一前沿的ε不可行解 → 支配第一前沿的ε不可行解
            selected = obj.handleConstraintViolationComparison(old_obj, new_obj, old_con, new_con);
        end

        function selected = handleType3ToType4(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 3→4: 支配第一前沿的ε不可行解 → 被第一前沿支配的ε不可行解
            selected = obj.handleConstraintViolationComparison(old_obj, new_obj, old_con, new_con);
        end

        function selected = handleType3ToType5(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 3→5: 支配第一前沿的ε不可行解 → 完全不可行解
            selected = obj.handleConstraintViolationComparison(old_obj, new_obj, old_con, new_con);
        end

        function selected = handleConstraintViolationComparison(obj, old_obj, new_obj, old_con, new_con)
            % 基于约束违反程度的通用比较逻辑
            % 规则：约束违反程度必须等于或小于旧个体，否则不接受；新解支配旧解 → 接受新解；互不支配 →比较约束违反程度；约束违反程度新解更小 → 接受新解

            old_cv = sum(max(0, old_con));
            new_cv = sum(max(0, new_con));

            % 约束违反程度必须等于或小于旧个体
            if new_cv > old_cv
                selected = false;
                return;
            end

            dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
            if dom_result == 1
                % 新解支配旧解
                selected = true;
            elseif dom_result == 0
                % 互不支配，比较约束违反程度
                selected = (new_cv < old_cv);
            else
                % 旧解支配新解
                selected = false;
            end
        end

        % ========== 被第一前沿支配或互不支配的ε不可行解的处理函数 ==========
        function selected = handleType4ToType1(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values)
            % 4→1: 被第一前沿支配或互不支配的ε不可行解 → 第一前沿可行解
            selected = obj.handleType4EpsilonFeasibleSolution(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, first_layer_flags, epsilon_values);
        end

        function selected = handleType4ToType2(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values)
            % 4→2: 被第一前沿支配或互不支配的ε不可行解 → 非第一前沿可行解
            selected = obj.handleType4EpsilonFeasibleSolution(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, first_layer_flags, epsilon_values);
        end

        function selected = handleType4ToType3(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values)
            % 4→3: 被第一前沿支配或互不支配的ε不可行解 → 支配第一前沿的ε不可行解
            selected = obj.handleType4EpsilonFeasibleSolution(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, first_layer_flags, epsilon_values);
        end

        function selected = handleType4ToType4(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values)
            % 4→4: 被第一前沿支配或互不支配的ε不可行解 → 被第一前沿支配或互不支配的ε不可行解
            selected = obj.handleType4EpsilonFeasibleSolution(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, first_layer_flags, epsilon_values);
        end

        function selected = handleType4ToType5(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values)
            % 4→5: 被第一前沿支配或互不支配的ε不可行解 → 完全不可行解
            selected = obj.handleType4EpsilonFeasibleSolution(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, first_layer_flags, epsilon_values);
        end

        function selected = handleType4EpsilonFeasibleSolution(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, first_layer_flags, epsilon_values)
            % Type4 ε可行解的通用处理逻辑
            % 规则：必须不违反约束放松，如果违反约束放松，则不接受新解；新解支配旧解 → 接受新解；互不支配 → 被前沿解支配次数；支配次数一样→检查多样性是否比旧解好；多样性没有旧解好或相等 → 拒绝新解

            % 检查新解是否违反约束放松
            if ~Woven_Utility.checkEpsilonFeasibility(new_con, epsilon_values)
                selected = false;
                return;
            end

            dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
            if dom_result == 1
                % 新解支配旧解
                selected = true;
            elseif dom_result == 0
                % 互不支配，比较被第一前沿解支配次数
                old_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(old_obj, PopObj, first_layer_flags);
                new_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(new_obj, PopObj, first_layer_flags);

                if new_dominated_count < old_dominated_count
                    % 被支配次数更少
                    selected = true;
                elseif new_dominated_count == old_dominated_count
                    % 被支配次数相等，比较多样性
                    selected = obj.Stage2SelectionScore(old_dec, old_obj, new_dec, new_obj, index, PopDec, PopObj);
                else
                    % 被支配次数更多
                    selected = false;
                end
            else
                % 旧解支配新解
                selected = false;
            end
        end

        % ========== 完全不可行解的处理函数 ==========
        function selected = handleType5ToType1(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 5→1: 完全不可行解 → 第一前沿可行解
            selected = obj.handleSimpleConstraintViolationComparison(old_con, new_con);
        end

        function selected = handleType5ToType2(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 5→2: 完全不可行解 → 非第一前沿可行解
            selected = obj.handleSimpleConstraintViolationComparison(old_con, new_con);
        end

        function selected = handleType5ToType3(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 5→3: 完全不可行解 → 支配第一前沿的ε不可行解
            selected = obj.handleSimpleConstraintViolationComparison(old_con, new_con);
        end

        function selected = handleType5ToType4(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 5→4: 完全不可行解 → 被第一前沿支配的ε不可行解
            selected = obj.handleSimpleConstraintViolationComparison(old_con, new_con);
        end

        function selected = handleType5ToType5(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags)
            % 5→5: 完全不可行解 → 完全不可行解
            selected = obj.handleSimpleConstraintViolationComparison(old_con, new_con);
        end

        function selected = handleSimpleConstraintViolationComparison(obj, old_con, new_con)
            % 简单约束违反程度比较
            % 规则：比较约束违反程度，谁小接受谁
            old_cv = sum(max(0, old_con));
            new_cv = sum(max(0, new_con));
            selected = (new_cv < old_cv);
        end

        function selected = Stage3EnvironmentSelection(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, selection_case, Problem)
            % 第三阶段环境选择函数 - 基于新策略的4种情况选择逻辑
            %
            % 选择策略：
            % 1. 可行解→可行解:
            %    - 新解支配旧解 → 接受新解
            %    - 互不支配 → 检查多样性是否比旧解好
            %    - 多样性没有旧解好或相等 → 拒绝新解
            % 2. 可行解→不可行解: 直接拒绝
            % 3. 不可行解→可行解:
            %    - 多样性更好，则接受新解
            %    - 否则拒绝
            % 4. 不可行解→不可行解:
            %    - 前50%：如果在第一前沿支配次数相同或更少的情况下CV更小则接受，否则拒绝
            %    - 后50%：直接强制收敛CV，确保结果好一点
            %
            % 输入参数:
            %   old_obj, new_obj: 父代和子代的目标函数值
            %   old_dec, new_dec: 父代和子代的决策变量
            %   old_con, new_con: 父代和子代的约束违反值
            %   index: 当前个体在种群中的索引
            %   PopDec, PopObj, PopCon: 整个种群的决策变量、目标函数值、约束违反值
            %   first_layer_flags: 第一前沿解的标记（只包括真实可行解）
            %   selection_case: 选择情况标识字符串
            %   Problem: 问题实例，用于获取当前评估次数
            % 返回: selected - true表示选择子代，false表示选择父代
            
            switch selection_case
                case 'feasible_to_feasible'
                    % 情况1：可行解 → 可行解
                    % 比较支配关系，互不支配比较多样性

                    dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
                    if dom_result == 1
                        % 新解支配旧解 → 接受新解
                        selected = true;
                    elseif dom_result == -1
                        % 旧解支配新解 → 拒绝新解
                        selected = false;
                    else
                        % 互不支配 → 比较多样性
                        selected = obj.Stage3SelectionScore(old_dec, old_obj, new_dec, new_obj, index, PopDec, PopObj);
                    end

                case 'feasible_to_infeasible'
                    % 情况2：可行解 → 不可行解
                    % 直接拒绝
                    selected = false;

                case 'infeasible_to_feasible'
                    % 情况3：不可行解 → 可行解
                    % 直接接受
                    selected = true;

                case 'infeasible_to_infeasible'
                    % 情况4：不可行解 → 不可行解
                    % 约束违反程度，谁低选谁，一样的话比较支配关系

                    old_cv = Woven_Utility.calculateConstraintViolation(old_con);
                    new_cv = Woven_Utility.calculateConstraintViolation(new_con);

                    if new_cv < old_cv
                        % 新解约束违反程度更低 → 接受新解
                        selected = true;
                    elseif new_cv > old_cv
                        % 旧解约束违反程度更低 → 拒绝新解
                        selected = false;
                    else
                        % 约束违反程度相同 → 比较支配关系
                        dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
                        if dom_result == 1
                            % 新解支配旧解 → 接受新解
                            selected = true;
                        elseif dom_result == -1
                            % 旧解支配新解 → 拒绝新解
                            selected = false;
                        else
                            % 互不支配 → 保持旧解（拒绝新解）
                            selected = false;
                        end
                    end
                    
                otherwise
                    error('Stage3EnvironmentSelection: 未知的选择情况 - %s', selection_case);
            end
        end
    end
end