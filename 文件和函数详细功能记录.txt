第三阶段mutate暂时使用的是第一阶段的存档，是否合理后续再试


函数书写和位置安排

文件架构

1、Woven 主函数 包括 主函数逻辑   和 三个阶段函数 组成 
2、Woven_Mutation 生成个体策略   用于产生子代个体   函数应该包括 三个阶段对应的个体生成策略和相应需要用到的子函数
3、Woven_Selection 选择个体策略 用于筛选子代个体 函数包括三阶段比较方法，同时因为选择方案不同，具体实现起来也应该有所区分，具体应该表现在使用不同函数名来区分各阶段使用的不同函数，还是应该使用多态的方式。如果相似的话，使用多态当然是无可厚非的，但是当前问题是，不仅仅是具体对于数据处理的实现逻辑不同，而且他们需要的输入参数和是否需要存储之前运行的数据等都不相同，我应该怎么做呢。
4、Woven_StageControl 用于阶段切换和控制，直接运行输出是否进行阶段切换即可   当然，相关的子函数也需要在本文件具体实现说

5、Woven_Record 用于集中需要修改的变量的代码，

接下来应该做的代码层面的改进

1、不能依靠AI批量生成代码然后直接应用   最好的方式还是他生成，然后在自己编写或审查一遍，除非是大批量多文件结构性调整，可以粗略审查一遍   
2、确保修改后的代码应该满足如下几个要求  1、变量名称规范，不能有任何歧义   2、函数命名规范 并且处于他应该在的地方 3、函数的输入变量和输出变量数量和内容应该合理 而不是反复要求 4、实现的函数不要重复 5、不要有太多的冗余错误检查 6、针对于多目标优化算法，不要有太多代码优化层面的东西   


具体的设计方案  


还有就是需要明确每个个体需要存储那些信息，如何存储和读取、如何刷新的问题 ，这是一个急需解决的问题    

要存储这些信息的前提应该是后续是需要被用到的数据，如果是不需要被用到的数据，自然是不需要保存的



单独写单独的函数，而不是要注意代码的多态问题，   或者就是保证输入一致，将一些数据存放在  某些确定的地方
其实这里是用不到多态的，只需要一个数字参数实现，或者如果差距过大的话实现不同的函数就可以了，没必要为了优化代码而优化，你认为对吗

使用子文件的属性保存，也就是实例化一个对象，然后在使用中进行对应属性的调用，，因此这类方法就必须改编为动态方法而不是静态方法了？

或者定义一个专门用来存储外部数据的小类，然后就可以传入他，在每个阶段读取对应变量名里面的值即可

具体的个体删除策略     1、首先，个体的数量一定是多余初始值N的，因为每次起始都是从N这个数量开始的    ，

因此每次删除一个个体后都需要和N进行对比，如果发现数量满足了，就停止删除了  。2、  删除策略应该是基于 COS角度重复，为什么呢？因为这种是在DE算法条件下最小概率提供给子代优质基因的情况，因此当然要优先删除掉  ，，，紧接着应该使用 



DE算法的变种  和当前环境的确定？这个点是否需要用到本算法中，是否需要用在本算法中去？

1、因为第一阶段的任务是一个单独、独立的任务，因此使用SHADE算法，是一个很完整的寻找不考虑约束多目标优化算法的好办法

2、一阶段切换需要把自适应计算阈值部分的代码加进去，具体应该加入到哪里？  按照标准化代码，还是应该写入到子函数中去
	阶段切换需要用到的参数  （前两处变量只需要传入Problem类即可）3、







