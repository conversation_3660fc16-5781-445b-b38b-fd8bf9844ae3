# Woven: 三阶段约束多目标进化算法

## 项目概述

Woven是一个创新的约束多目标优化算法，采用三阶段渐进式策略来处理复杂的约束多目标优化问题。算法名称"Woven"寓意着将不同优化策略巧妙地"编织"在一起，形成一个统一而强大的优化框架。

![Algorithm Version](https://img.shields.io/badge/Version-第三阶段收敛性提升版12111-blue)
![Language](https://img.shields.io/badge/Language-MATLAB-orange)
![Algorithm Type](https://img.shields.io/badge/Type-约束多目标优化-green)

## 🚀 核心创新点

### 1. 三阶段渐进式策略
- **阶段一**：无约束多目标优化，专注于探索Pareto前沿的基本形状
- **阶段二**：引入ε约束法，逐步收紧约束，实现从不可行域到可行域的平滑过渡
- **阶段三**：严格约束处理，精确收敛到约束Pareto前沿

### 2. 自适应阶段转换机制
- 基于非支配解比例和目标空间收敛性的智能转换条件
- 边界交织检测算法，确保可行解与不可行解的适当分布
- 几何分布质量评估，保证解集的空间分布特性

### 3. 多层次选择策略
- **第一阶段**：基于支配关系和MED距离的选择
- **第二阶段**：9种情况的环境选择策略，考虑ε约束可行性
- **第三阶段**：4种情况的精确选择，强化收敛性

### 4. 混合变异算子
- 结合SHADE自适应差分进化
- 多种DE变异策略：DE/transfer/1, DE/rand/1, DE/rand/2
- 自适应参数控制和多样性增强机制

## 📁 项目架构

```
Woven/
├── Woven.m                    # 主算法文件
├── Woven_Selection.m          # 选择策略实现
├── Woven_Mutation.m           # 变异算子实现
├── Woven_StageControl.m       # 阶段控制逻辑
├── Woven_Record.m             # 参数管理（单例模式）
├── Woven_Utility.m            # 工具函数集合
├── 文件和函数详细功能记录.txt   # 开发记录
├── codeGeneration.txt         # 代码规范
└── README.md                  # 项目文档
```

### 架构图

```mermaid
graph TB
    A[Woven.m 主算法] --> B[Woven_Record.m 参数管理]
    A --> C[Woven_Mutation.m 变异算子]
    A --> D[Woven_Selection.m 选择策略]
    A --> E[Woven_StageControl.m 阶段控制]
    A --> F[Woven_Utility.m 工具函数]
    
    B --> B1[SHADE参数存储]
    B --> B2[阶段参数配置]
    B --> B3[权重系数管理]
    
    C --> C1[阶段1: GA+DE/transfer/1]
    C --> C2[阶段2: 正态+DE/rand/1]
    C --> C3[阶段3: 正态+DE/rand/2]
    
    D --> D1[阶段1: 支配+MED]
    D --> D2[阶段2: ε约束9情况]
    D --> D3[阶段3: 精确4情况]
    
    E --> E1[收敛性检测]
    E --> E2[边界交织检测]
    E --> E3[几何分布评估]
    
    F --> F1[支配关系计算]
    F --> F2[距离度量]
    F --> F3[多样性评估]
```

## 📚 文件详解

### 核心文件说明

#### `Woven.m` - 主算法控制器
主算法文件，实现三阶段优化逻辑：
- **`main(Algorithm, Problem)`**: 算法主循环，协调各组件工作
- **`firstStage()`**: 第一阶段优化 - 无约束多目标探索
- **`secondStage()`**: 第二阶段优化 - ε约束渐进收敛
- **`thirdStage()`**: 第三阶段优化 - 精确约束优化

**关键特性**：
- 自动阶段转换机制
- 统一的种群管理
- 完整的评估次数控制

#### `Woven_Selection.m` - 智能选择策略
选择策略实现，包含三个阶段的不同选择逻辑：

**阶段1选择**：
- 基于支配关系的主要选择
- MED距离作为辅助判别准则
- 被支配次数比较机制

**阶段2选择**：
- 9种约束状态组合的完整处理
- ε约束可行性判断
- 多因子加权评分系统

**阶段3选择**：
- 4种精确收敛情况
- 时间窗口自适应策略
- 强化收敛保证机制

#### `Woven_Mutation.m` - 多样化变异策略
变异算子实现，提供阶段特化的个体生成策略：

**阶段1变异**：
- GA高斯变异（概率：50%）
- DE/transfer/1策略（概率：50%）
- 自适应边界处理

**阶段2变异**：
- 正态变异（概率：99%）
- DE/rand/1策略（概率：1%）
- 固定扰动幅度

**阶段3变异**：
- 正态变异（概率：99%）
- DE/rand/2策略（概率：1%）
- 增强随机性和多样性

**SHADE集成**：
- 自适应CR和F参数
- 历史成功记录管理
- 存档机制维护

#### `Woven_StageControl.m` - 智能阶段转换
阶段转换控制，实现基于问题特征的智能切换：

**阶段1→2转换条件**：
- 非支配解比例 ≥ 60%
- 前两层解比例 ≥ 93%
- 目标空间收敛性检测
- 最小/最大评估次数控制

**阶段2→3转换条件**：
- 边界交织条件检测
- 几何分布质量评估
- 5点角度分布验证
- 最大评估次数保底

**创新算法**：
- 自适应阈值计算：λ = 10^(M-2) × Obj / (M × N)
- 边界重叠度量
- 非支配解几何分布分析

#### `Woven_Record.m` - 集中参数管理
参数管理和存储，采用单例模式确保全局一致性：

**SHADE参数管理**：
- 记忆库大小和存档率控制
- CR/F参数的历史记录
- 成功参数的权重更新

**阶段参数配置**：
- 各阶段变异概率设置
- 扰动幅度和比例控制
- 转换条件阈值管理

**权重系数设定**：
- 第二阶段：决策空间多样性(10%) + 角度(30%) + MED(60%)
- 第三阶段：角度(30%) + MED(60%) + 拥挤距离(10%)

#### `Woven_Utility.m` - 核心计算工具
工具函数集合，提供算法所需的各种数学计算：

**距离度量**：
- `calculateCrowdingDistance()`: 经典拥挤距离
- `calculateMEDDistance()`: 支配关系欧几里得距离
- `Stage2DecisionSpaceDiversity()`: 决策空间多样性

**支配关系**：
- `checkDominance()`: 高效支配关系判断
- `calculateStage1NonDominatedSet()`: 第一阶段非支配解
- `calculateStage23NonDominatedSet()`: 考虑约束的非支配解

**约束处理**：
- `calculateEpsilonValues()`: 动态ε值计算
- `checkEpsilonFeasibility()`: ε约束可行性检查

**评估指标**：
- `Stage2AngleScore()`: 基于理想点的角度评分
- `calculateAdaptiveThreshold()`: 自适应收敛阈值

## 🔄 算法执行流程

```mermaid
flowchart TD
    A[初始化种群] --> B[阶段1: 无约束多目标优化]
    B --> C{阶段1转换条件检查}
    C -->|非支配解<60%或前两层<93%| B
    C -->|满足条件| D[阶段2: ε约束优化]
    D --> E{阶段2转换条件检查}
    E -->|边界未交织或几何分布不佳| D
    E -->|满足条件| F[阶段3: 严格约束优化]
    F --> G{算法终止条件}
    G -->|评估次数未达上限| F
    G -->|达到终止条件| H[输出约束Pareto前沿]
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style D fill:#f3e5f5
    style F fill:#e8f5e8
    style H fill:#c8e6c9
```

## ⚙️ 核心算法特性

### 1. 智能阶段转换机制

#### 第一阶段转换条件
```matlab
% 基本条件检查
min_fe_reached = Problem.FE >= Problem.maxFE * stage1_min_fe;
max_fe_reached = Problem.FE >= Problem.maxFE * stage1_max_fe;

% 解质量条件
pareto_front_ratio = non_dominated_count / N >= first_front_ratio;
combined_ratio = (first_layer + second_layer) / N >= non_dominated_threshold;

% 收敛性条件
lambda = 10^(M-2) * mean(max(PopObj) - min(PopObj)) / (M * N);
convergence = consecutive_no_change >= consecutive_gen_threshold;
```

#### 第二阶段转换条件
```matlab
% 边界交织检测
for i = 1:M
    overlap(i) = max(0, min(feasible_max(i), infeasible_max(i)) - 
                     max(feasible_min(i), infeasible_min(i)));
    total_range(i) = max(feasible_max(i), infeasible_max(i)) - 
                     min(feasible_min(i), infeasible_min(i));
end
interwoven_ratio = overlap ./ (total_range + eps);
interwoven_condition = all(interwoven_ratio >= interwoven_threshold);
```

### 2. ε约束法动态实现

#### 动态ε值计算
```matlab
% 时间进度归一化
progress = min(1, elapsed_fe / max_fe);

% ε值平滑下降
cp = (1 - progress)^(1/constraint_beta);
epsilon_values(j) = initial_epsilon * cp;
```

#### 九种选择情况处理
| 父代状态 | 子代状态 | 选择策略 |
|---------|---------|---------|
| 可行解 | 可行解 | 支配关系 + 多样性 |
| 可行解 | ε可行解 | 支配关系 + 多样性要求 |
| 可行解 | 不可行解 | 直接拒绝 |
| ε可行解 | 可行解 | 接受 + 多样性检查 |
| ε可行解 | ε可行解 | 支配关系 + 第一前沿支配次数 |
| ε可行解 | 不可行解 | 直接拒绝 |
| 不可行解 | 可行解 | 直接接受 |
| 不可行解 | ε可行解 | 直接接受 |
| 不可行解 | 不可行解 | 约束违反值比较 |

### 3. 多样性保持机制

#### 角度评分计算
```matlab
% 理想点计算
ideal_point = min(PopObj);

% 当前解向量归一化
vec_i = (obj_i - ideal_point) / norm(obj_i - ideal_point);

% 与其他解的夹角计算
for j = 1:N
    vec_j = (PopObj(j,:) - ideal_point) / norm(PopObj(j,:) - ideal_point);
    cos_angle = max(-1, min(1, vec_i * vec_j'));
    angles(j) = acos(cos_angle);
end

% 角度评分：平均角度 - 标准差权重
angle_score = mean(angles) - std_weight * std(angles);
```

#### MED距离度量
```matlab
% 计算支配的解的数量和距离
for i = 1:N
    if dominates(obj_a, PopObj(i,:))
        dominated_count++;
        dominated_distance += euclidean_distance(obj_a, PopObj(i,:));
    end
end

% MED评分计算
if dominated_count > 0
    base_score = (dominated_distance / dominated_count) * 
                 (1 + log(dominated_count + 1));
end
```

## 🛠️ 使用方法

### 基本使用
```matlab
% 创建问题实例（需要实现PROBLEM接口）
Problem = YourConstrainedProblem();

% 创建算法实例
Algorithm = Woven('memory_size', 5, 'archive_rate', 1.0);

% 运行算法
Algorithm.main(Problem);
```

### 参数自定义
```matlab
% 获取参数记录实例
record = Woven_Record.getInstance();

% 自定义阶段参数
record.stage1_max_fe = 0.4;        % 第一阶段最大评估比例
record.stage2_max_fe = 0.8;        % 第二阶段最大评估比例
record.constraint_beta = 0.2;      % 约束收缩指数

% 自定义选择权重
record.stage2_factor_weights.angle = 0.4;
record.stage2_factor_weights.med = 0.5;
record.stage2_factor_weights.decision_space_diversity = 0.1;
```

### 问题接口要求
```matlab
classdef YourProblem < PROBLEM
    methods
        function Population = Initialization(obj)
            % 返回初始化种群
        end
        
        function offspring = Evaluation(obj, DecisionVariables)
            % 评估决策变量，返回包含objs和cons的SOLUTION对象
        end
        
        function result = NotTerminated(obj, Population)
            % 检查是否满足终止条件
        end
    end
    
    properties
        FE        % 当前评估次数
        maxFE     % 最大评估次数
        N         % 种群大小
        M         % 目标数量
        D         % 决策变量维度
    end
end
```

## 📊 性能特性

### 计算复杂度
- **时间复杂度**: O(N²M) 每代
- **空间复杂度**: O(N×(D+M+C)) 其中C为约束数量
- **收敛性**: 理论保证收敛到约束Pareto前沿

### 适用问题规模
- **种群大小**: 推荐100-500
- **目标维度**: 2-10个目标
- **决策变量**: 10-1000维
- **约束数量**: 1-50个约束

### 性能优势
1. **收敛速度**: 三阶段策略显著提升收敛效率
2. **解集质量**: 多层次选择保证解的分布性和收敛性
3. **约束处理**: ε约束法避免了传统罚函数的参数调节困难
4. **鲁棒性**: 自适应参数机制增强算法稳定性

## 🔬 技术亮点

### 1. 工程化设计特色
- **模块化架构**: 清晰的功能分离，便于维护和扩展
- **单例模式**: 参数管理的高效实现，避免重复初始化
- **错误处理**: 完善的边界条件检查和异常处理
- **接口规范**: 统一的问题接口，易于集成不同问题

### 2. 算法理论创新
- **渐进式约束处理**: 首次提出三阶段约束处理策略
- **边界交织理论**: 创新的可行域-不可行域分布评估方法
- **几何分布质量**: 基于角度分布的解集质量评估准则
- **多层次选择**: 不同阶段的专门化选择策略设计

### 3. 计算效率优化
- **快速支配检查**: 优化的支配关系计算算法
- **增量更新**: 存档和记忆库的增量维护机制
- **内存管理**: 高效的数据结构和存储策略
- **并行潜力**: 算法结构天然支持并行化改造

## 🎯 适用领域

### 工程设计优化
- 结构设计优化（桥梁、建筑等）
- 机械系统设计（齿轮箱、发动机等）
- 电路设计优化
- 航空航天器设计

### 资源分配问题
- 投资组合优化
- 供应链管理
- 任务调度问题
- 能源系统优化

### 参数调优问题
- 机器学习超参数优化
- 控制系统参数调节
- 信号处理参数设定
- 算法参数自动配置

### 多学科设计优化
- 汽车设计（燃油经济性vs性能vs安全性）
- 化工过程优化
- 生物医学工程
- 环境工程设计

## 🚀 未来发展方向

### 短期扩展计划
1. **大规模优化**: 扩展到高维决策空间（>1000维）
2. **多模态处理**: 集成niching技术处理多峰问题
3. **GPU并行**: 利用GPU加速种群计算
4. **可视化工具**: 开发实时优化过程可视化界面

### 中期发展目标
1. **动态优化**: 处理时变约束和目标函数
2. **不确定性优化**: 集成鲁棒性和可靠性考虑
3. **多层次优化**: 支持双层和多层优化问题
4. **自适应框架**: 基于问题特征的自动参数调节

### 长期研究方向
1. **智能化算法**: 集成机器学习进行策略自适应
2. **云计算集成**: 分布式大规模优化平台
3. **实时优化**: 在线优化和控制应用
4. **领域特化**: 针对特定领域的算法定制

## 📚 参考文献与致谢

### 理论基础
- NSGA-II算法的多目标优化理论
- SHADE算法的自适应差分进化机制
- ε约束法的约束处理策略
- Pareto最优理论和多目标优化基础

### 开发团队
本算法由多目标优化研究团队开发，集成了团队在约束处理、自适应算法、多目标优化等领域的最新研究成果。

---

## 📄 许可证

本项目采用学术研究许可证，允许学术研究和教育用途的免费使用。商业用途请联系开发团队获取授权。

## 📞 联系方式

如有技术问题、使用建议或合作意向，欢迎通过以下方式联系：

- 技术问题：请在项目Issues中提出
- 学术合作：欢迎学术交流与合作
- 应用咨询：可提供工程应用技术支持

---

**版本信息**: 第三阶段收敛性提升版12111  
**最后更新**: 2025年6月15日  
**文档版本**: v1.0

*Woven算法 - 编织智能优化的未来*

---

# 算法运行逻辑详解

## 算法整体执行逻辑

### 主循环结构

Woven算法的主执行逻辑位于`main()`函数中，采用while循环直到满足终止条件：

```matlab
while Algorithm.NotTerminated(Population)
    switch Algorithm.stage
        case 1: [PopDec, PopObj, PopCon] = Algorithm.firstStage(Problem, PopDec, PopObj, PopCon);
        case 2: [PopDec, PopObj, PopCon] = Algorithm.secondStage(Problem, PopDec, PopObj, PopCon);
        case 3: [PopDec, PopObj, PopCon] = Algorithm.thirdStage(Problem, PopDec, PopObj, PopCon);
    end
    Population = SOLUTION(PopDec, PopObj, PopCon);
end
```

### 初始化过程

1. **Record实例创建**：`Woven_Record.getInstance(memory_size=5, archive_rate=1.0, population_size=N)`
2. **组件初始化**：
   - `Algorithm.mutation = Woven_Mutation(Algorithm.record)`
   - `Algorithm.selection = Woven_Selection(Algorithm.record)`
   - `Algorithm.stageControl = Woven_StageControl(Algorithm.record)`
3. **种群初始化**：`Population = Problem.Initialization()`

## 第一阶段运行逻辑

### 阶段目标
专注于无约束多目标优化，探索Pareto前沿的基本形状，不考虑约束条件。

### 执行流程

#### 个体级迭代处理
对种群中每个个体i执行：

1. **获取当前状态**：
   ```matlab
   old_dec = PopDec(i,:);  % 父代决策变量
   old_obj = PopObj(i,:);  % 父代目标值
   ```

2. **生成子代**：
   ```matlab
   new_dec = Algorithm.mutation.mutate(old_dec, stage=1, old_obj, Problem);
   offspring = Problem.Evaluation(new_dec);
   new_obj = offspring.objs;
   new_con = offspring.cons;
   ```

3. **选择决策**：
   ```matlab
   selected = Algorithm.selection.Stage1Selection(old_dec, old_obj, new_dec, new_obj, new_con, i, PopDec, PopObj);
   ```

4. **更新种群**（如果子代被选中）：
   ```matlab
   if selected
       % 计算改进量
       obj_range = max(PopObj) - min(PopObj);
       normalized_diff = (old_obj - new_obj) ./ (obj_range + 1e-10);
       improvement = norm(normalized_diff);
       
       % 更新SHADE记忆
       Algorithm.mutation.updateMemorySHADE(old_dec, new_dec, improvement, PopDec);
       
       % 更新种群
       PopDec(i,:) = new_dec;
       PopObj(i,:) = new_obj;
       PopCon(i,:) = new_con;
   end
   ```

#### 一代结束处理
```matlab
Algorithm.mutation.updateArchiveSHADE(PopDec);  % 更新存档
```

### 第一阶段选择逻辑

`Stage1Selection`函数的详细逻辑：

1. **支配关系检查**：
   ```matlab
   dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
   ```

2. **情况1：新解支配旧解** (`dom_result == 1`)
   ```matlab
   selected = true;  % 直接接受
   ```

3. **情况2：互不支配** (`dom_result == 0`)
   ```matlab
   % 计算当前种群非支配解
   non_dominated_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);
   
   % 计算被支配次数
   old_BeDomCT = Woven_Utility.calculateDominatedByNonDominatedSet(old_obj, PopObj, non_dominated_flags);
   
   % 计算新解的被支配次数（需要临时更新种群）
   temp_PopObj = PopObj;
   temp_PopObj(index, :) = new_obj;
   temp_non_dominated_flags = Woven_Utility.calculateStage1NonDominatedSet(temp_PopObj);
   new_BeDomCT = Woven_Utility.calculateDominatedByNonDominatedSet(new_obj, temp_PopObj, temp_non_dominated_flags);
   
   if new_BeDomCT < old_BeDomCT
       selected = true;  % 被支配次数更少
   elseif new_BeDomCT == old_BeDomCT
       % 被支配次数相同，比较MED距离
       old_med = Woven_Utility.calculateMEDDistance(old_obj, index, PopObj);
       new_med = Woven_Utility.calculateMEDDistance(new_obj, index, temp_PopObj);
       selected = (new_med > old_med);
   else
       selected = false;  % 被支配次数更多
   end
   ```

4. **情况3：旧解支配新解** (`dom_result == -1`)
   ```matlab
   selected = false;  % 直接拒绝
   ```

### 第一阶段变异逻辑

在`mutate(candidate, stage=1, fitness, Problem)`中：

1. **策略选择**（基于概率`stage1_normal_prob = 0.5`）：

   **策略A：GA高斯变异**（概率50%）
   ```matlab
   k = randi(D);  % 随机选择一个维度
   distance_to_boundary = min(candidate(k), 1 - candidate(k));
   sigma = min(0.5, distance_to_boundary * stage1_sigma_scale);
   temp = candidate(k) + randn * sigma;
   if temp >= 0 && temp <= 1
       offspring(k) = temp;
   end
   ```

   **策略B：DE/transfer/1变异**（概率50%）
   ```matlab
   if archive_size >= 2
       % 从存档中随机选择2个个体
       indices = randperm(population_size, 2);
       r1 = archive(indices(1), :);
       r2 = archive(indices(2), :);
       
       % 动态F值
       F = de_f_min + (de_f_max - de_f_min) * rand;
       
       % DE/transfer/1: v = candidate + F * (r1 - r2)
       v = candidate + F * (r1 - r2);
       offspring = max(0, min(1, v));  % 边界处理
   end
   ```

### 第一阶段转换条件检查

在每代结束后执行`checkStage1Transition`：

1. **最大评估次数检查**：
   ```matlab
   max_eval_count = Problem.FE >= Problem.maxFE * stage1_max_fe;
   if max_eval_count, transition = true; return; end
   ```

2. **最小评估次数检查**：
   ```matlab
   min_eval_count = Problem.FE >= Problem.maxFE * stage1_min_fe;
   if ~min_eval_count, transition = false; return; end
   ```

3. **解质量检查**：
   ```matlab
   % 计算非支配解比例
   obj_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);
   non_dominated_count = sum(obj_flags == 1);
   pareto_front_ratio = non_dominated_count / N;
   
   if pareto_front_ratio < first_front_ratio  % 通常为0.60
       transition = false; return;
   end
   
   % 计算二层解
   second_layer_flags = Woven_Utility.calculateSecondLayerSolutions(PopObj, obj_flags);
   combined_ratio = (non_dominated_count + sum(second_layer_flags)) / N;
   
   if combined_ratio < non_dominated_threshold  % 通常为0.93
       transition = false; return;
   end
   ```

4. **收敛性检查**：
   ```matlab
   % 计算目标空间变化
   current_change = Woven_Utility.calculateObjectiveChange(PopObj, previous_objectives);
   lambda = Woven_Utility.calculateAdaptiveThreshold(PopObj, M, N);
   
   if current_change <= lambda
       consecutive_nochange_count++;
   else
       consecutive_nochange_count = 0;
   end
   
   convergence_condition = consecutive_nochange_count >= consecutive_gen_threshold;
   transition = convergence_condition;
   ```

## 第二阶段运行逻辑

### 阶段目标
引入ε约束法，逐步收紧约束条件，实现从不可行域到可行域的平滑过渡。

### 执行流程

#### ε值计算
每代开始时计算当前的ε约束值：
```matlab
stage2_elapsed_fe = Problem.FE - Algorithm.record.stage2_start_fe;
stage2_max_fe = Problem.maxFE * Algorithm.record.stage2_max_fe - Algorithm.record.stage2_start_fe;
epsilon_values = Woven_Utility.calculateEpsilonValues(PopCon, stage2_elapsed_fe, stage2_max_fe, Algorithm.record.constraint_beta);
```

#### 第一前沿解计算
```matlab
first_layer_flags = Woven_Utility.calculateStage23NonDominatedSet(PopObj, PopCon, Problem);
```

#### 个体级处理
对每个个体执行：

1. **生成子代**：
   ```matlab
   new_dec = Algorithm.mutation.mutate(old_dec, stage=2);
   offspring = Problem.Evaluation(new_dec);
   ```

2. **选择决策**：
   ```matlab
   selected = Algorithm.selection.Stage2Selection(old_dec, old_obj, old_con, new_dec, new_obj, new_con, i, PopDec, PopObj, PopCon, epsilon_values, first_layer_flags);
   ```

### 第二阶段选择逻辑

`Stage2Selection`执行九种情况的完整分类：

#### 可行性判断
```matlab
% 真实可行性
parent_feasible = sum(max(0, old_con)) <= 0;
child_feasible = sum(max(0, new_con)) <= 0;

% ε约束可行性
parent_epsilon_feasible = Woven_Utility.checkEpsilonFeasibility(old_con, epsilon_values);
child_epsilon_feasible = Woven_Utility.checkEpsilonFeasibility(new_con, epsilon_values);
```

#### 类型分类
```matlab
% 父代类型
if parent_feasible
    parent_type = 1;  % 可行解
elseif parent_epsilon_feasible
    parent_type = 2;  % 收敛范围内解
else
    parent_type = 3;  % 完全不可行解
end

% 子代类型（同样逻辑）
```

#### 九种选择情况

**情况1：可行解→可行解**
```matlab
dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
if dom_result == 1
    selected = true;  % 新解支配旧解
elseif dom_result == 0
    selected = obj.Stage2SelectionScore(...);  % 比较多样性
else
    selected = false;  % 旧解支配新解
end
```

**情况2：可行解→ε可行解**
```matlab
dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
if dom_result == 1
    diversity_better = obj.Stage2SelectionScore(...);
    selected = diversity_better;  % 支配且多样性更好才接受
else
    selected = false;
end
```

**情况3：可行解→不可行解**
```matlab
selected = false;  % 直接拒绝
```

**情况4：ε可行解→可行解**
```matlab
dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
if dom_result == 1
    selected = true;
elseif dom_result == 0
    % 比较被第一前沿支配次数
    old_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(old_obj, PopObj, first_layer_flags);
    new_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(new_obj, PopObj, first_layer_flags);
    
    if new_dominated_count < old_dominated_count
        selected = true;
    elseif new_dominated_count == old_dominated_count
        selected = obj.Stage2SelectionScore(...);  % 比较多样性
    else
        selected = false;
    end
else
    selected = false;
end
```

**情况5：ε可行解→ε可行解**
```matlab
% 逻辑与情况4相同
```

**情况6：ε可行解→不可行解**
```matlab
selected = false;  % 直接拒绝
```

**情况7：不可行解→可行解**
```matlab
selected = true;  % 直接接受
```

**情况8：不可行解→ε可行解**
```matlab
selected = true;  % 直接接受
```

**情况9：不可行解→不可行解**
```matlab
old_cv = sum(max(0, old_con));
new_cv = sum(max(0, new_con));
selected = (new_cv < old_cv);  % 约束违反值更小则接受
```

### 第二阶段变异逻辑

```matlab
if rand < stage2_normal_prob  % 概率99%
    % 正态变异
    k = randi(D);
    temp = candidate(k) + randn * stage2_sigma;
    if temp >= 0 && temp <= 1
        offspring(k) = temp;
    end
else
    % DE/rand/1变异
    if archive_size >= 3
        indices = randperm(archive_size, 3);
        r1 = archive(indices(1), :);
        r2 = archive(indices(2), :);
        r3 = archive(indices(3), :);
        
        F = rand;
        v = r1 + F * (r2 - r3);
        offspring = max(0, min(1, v));
    end
end
```

### 第二阶段转换条件检查

1. **边界交织条件**：
   ```matlab
   interwoven_condition = obj.checkInterwovenBoundaries(X, V, Cons);
   ```

2. **几何分布检查**：
   ```matlab
   if interwoven_condition
       geometric_condition = obj.checkGeometricDistribution(X, V, Cons, Problem);
       interwoven_condition = interwoven_condition && geometric_condition;
   end
   ```

3. **最终转换决策**：
   ```matlab
   max_eval_count = Problem.FE >= Problem.maxFE * stage2_max_fe;
   transition = interwoven_condition || max_eval_count;
   ```

#### 边界交织检测算法

```matlab
function result = checkInterwovenBoundaries(obj, X, V, Cons)
    [N, M] = size(V);
    feasible = sum(max(0, Cons), 2) <= 0;
    feasible_ratio = sum(feasible) / N;
    
    % 边界情况处理
    if feasible_ratio < 0.1
        result = false; return;
    elseif feasible_ratio >= 0.9
        result = true; return;
    end
    
    % 获取可行解和不可行解
    feasible_obj = V(feasible,:);
    infeasible_obj = V(~feasible,:);
    
    % 计算每个目标维度的重叠
    overlap = zeros(1, M);
    total_range = zeros(1, M);
    for i = 1:M
        feasible_min = min(feasible_obj(:,i));
        feasible_max = max(feasible_obj(:,i));
        infeasible_min = min(infeasible_obj(:,i));
        infeasible_max = max(infeasible_obj(:,i));
        
        overlap(i) = max(0, min(feasible_max, infeasible_max) - max(feasible_min, infeasible_min));
        total_range(i) = max(feasible_max, infeasible_max) - min(feasible_min, infeasible_min);
    end
    
    interwoven_ratio = overlap ./ (total_range + eps);
    result = all(interwoven_ratio >= interwoven_threshold);
end
```

#### 几何分布质量检测

```matlab
function result = checkGeometricDistribution(obj, X, V, Cons, Problem)
    % 获取考虑约束的非支配解
    obj_flags = Woven_Utility.calculateStage23NonDominatedSet(V, Cons, Problem);
    non_dominated_indices = find(obj_flags == 1);
    
    if length(non_dominated_indices) < 5
        result = false; return;
    end
    
    % 检查所有5个点的组合
    for i = 1:n-4
        for j = i+1:n-3
            for k = j+1:n-2
                for l = k+1:n-1
                    for m = l+1:n
                        points = [non_dominated_obj(i,:); non_dominated_obj(j,:);
                                 non_dominated_obj(k,:); non_dominated_obj(l,:);
                                 non_dominated_obj(m,:)];
                        
                        if obj.checkValidArrangement(points)
                            result = true; return;
                        end
                    end
                end
            end
        end
    end
    
    result = false;
end
```

## 第三阶段运行逻辑

### 阶段目标
严格约束处理，精确收敛到约束Pareto前沿，不再使用ε约束。

### 执行流程

#### 第一前沿解计算
```matlab
first_layer_flags = Woven_Utility.calculateStage23NonDominatedSet(PopObj, PopCon, Problem);
```

#### 个体级处理
```matlab
for i = 1:N
    old_dec = PopDec(i,:);
    old_obj = PopObj(i,:);
    old_con = PopCon(i,:);
    
    new_dec = Algorithm.mutation.mutate(old_dec, stage=3, old_obj, Problem);
    offspring = Problem.Evaluation(new_dec);
    
    selected = Algorithm.selection.Stage3Selection(old_dec, old_obj, old_con, new_dec, new_obj, new_con, i, PopDec, PopObj, PopCon, first_layer_flags, Problem);
    
    if selected
        PopDec(i,:) = new_dec;
        PopObj(i,:) = new_obj;
        PopCon(i,:) = new_con;
    end
end
```

### 第三阶段选择逻辑

基于严格约束可行性的四种情况：

#### 可行性判断
```matlab
parent_feasible = sum(max(0, old_con)) <= 0;
child_feasible = sum(max(0, new_con)) <= 0;
```

#### 四种选择情况

**情况1：可行解→可行解**
```matlab
dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
if dom_result == 1
    selected = true;  % 新解支配旧解
elseif dom_result == 0
    selected = obj.Stage3SelectionScore(...);  % 检查多样性
else
    selected = false;  % 旧解支配新解
end
```

**情况2：可行解→不可行解**
```matlab
selected = false;  % 直接拒绝
```

**情况3：不可行解→可行解**
```matlab
selected = true;  % 直接接受
```

**情况4：不可行解→不可行解**
```matlab
% 计算第三阶段进度
stage3_elapsed_fe = Problem.FE - obj.record.stage3_start_fe;
stage3_total_fe = Problem.maxFE - obj.record.stage3_start_fe;
stage3_progress = stage3_elapsed_fe / stage3_total_fe;

if stage3_progress <= 0.5
    % 前50%：考虑被支配次数
    old_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(old_obj, PopObj, first_layer_flags);
    new_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(new_obj, PopObj, first_layer_flags);
    
    if new_dominated_count <= old_dominated_count
        old_cv = sum(max(0, old_con));
        new_cv = sum(max(0, new_con));
        selected = (new_cv < old_cv);
    else
        selected = false;
    end
else
    % 后50%：强制收敛CV
    old_cv = sum(max(0, old_con));
    new_cv = sum(max(0, new_con));
    selected = (new_cv < old_cv);
end
```

### 第三阶段变异逻辑

```matlab
if rand < stage3_normal_prob  % 概率99%
    % 正态变异
    k = randi(D);
    temp = candidate(k) + randn * stage3_sigma;
    if temp >= 0 && temp <= 1
        offspring(k) = temp;
    end
else
    % DE/rand/2变异（如果存档个体足够）
    if archive_size >= 5
        indices = randperm(archive_size, 5);
        r1 = archive(indices(1), :);
        r2 = archive(indices(2), :);
        r3 = archive(indices(3), :);
        r4 = archive(indices(4), :);
        r5 = archive(indices(5), :);
        
        F = de_f_min + (de_f_max - de_f_min) * rand;
        v = r1 + F * (r2 - r3) + F * (r4 - r5);
        offspring = max(0, min(1, v));
    else
        % 退化为DE/rand/1或其他策略
    end
end
```

## 关键计算函数逻辑

### ε值动态计算

```matlab
function epsilon_values = calculateEpsilonValues(PopCon, elapsed_fe, max_fe, constraint_beta)
    progress = min(1, elapsed_fe / max_fe);
    [~, num_constraints] = size(PopCon);
    epsilon_values = zeros(1, num_constraints);
    
    for j = 1:num_constraints
        constraint_violations = max(0, PopCon(:, j));
        non_zero_violations = constraint_violations(constraint_violations > 0);
        
        if isempty(non_zero_violations)
            epsilon_values(j) = 0;
        else
            max_violation = max(non_zero_violations);
            initial_epsilon = max_violation * 0.7;
            
            % 幂函数下降
            cp = (1 - progress)^(1/constraint_beta);
            epsilon_values(j) = initial_epsilon * cp;
        end
    end
end
```

### 支配关系计算

```matlab
function dom_result = checkDominance(obj_a, obj_b)
    better_or_equal = obj_a <= obj_b;
    strictly_better = obj_a < obj_b;
    
    if all(better_or_equal) && any(strictly_better)
        dom_result = 1;   % a支配b
    elseif all(obj_b <= obj_a) && any(obj_b < obj_a)
        dom_result = -1;  % b支配a
    else
        dom_result = 0;   % 互不支配
    end
end
```

### MED距离计算

```matlab
function dist = calculateMEDDistance(obj_a, index, PopObj)
    [N, M] = size(PopObj);
    dominated_count = 0;
    dominated_distance = 0;
    
    for i = 1:N
        if i == index, continue; end
        
        % 检查obj_a是否支配PopObj(i,:)
        better_or_equal = true;
        at_least_one_better = false;
        for j = 1:M
            if obj_a(j) > PopObj(i,j)
                better_or_equal = false;
                break;
            elseif obj_a(j) < PopObj(i,j)
                at_least_one_better = true;
            end
        end
        
        if better_or_equal && at_least_one_better
            dominated_count = dominated_count + 1;
            dominated_distance = dominated_distance + sqrt(sum((PopObj(i,:) - obj_a).^2));
        end
    end
    
    if dominated_count > 0
        dist = dominated_distance / dominated_count * (1 + log(dominated_count + 1));
    else
        dist = 0;
    end
end
```

### 自适应阈值计算

```matlab
function lambda = calculateAdaptiveThreshold(PopObj, M, N)
    Obj = mean(max(PopObj) - min(PopObj));
    lambda = 10^(M-2) * Obj / (M * N);
end
```

## 算法终止条件

算法在以下情况下终止：
1. 达到最大评估次数：`Problem.FE >= Problem.maxFE`
2. 问题特定的终止条件：`Problem.NotTerminated(Population) == false`

整个算法的执行保证了从无约束探索到严格约束收敛的平滑过渡，通过三个阶段的专门化策略实现高质量约束多目标优化解集的获得。