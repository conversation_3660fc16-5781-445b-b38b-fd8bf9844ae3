# Woven: 三阶段约束多目标进化算法

## 🚀 核心创新点

### 1. 三阶段渐进式策略
- **阶段一**：无约束多目标优化，专注于探索Pareto前沿的基本形状
- **阶段二**：引入ε约束法，逐步收紧约束，实现从不可行域到可行域的平滑过渡
- **阶段三**：严格约束处理，精确收敛到约束Pareto前沿

### 2. 自适应阶段转换机制
- 基于非支配解比例和目标空间收敛性的智能转换条件
- 边界交织检测算法，确保可行解与不可行解的适当分布
- 几何分布质量评估，保证解集的空间分布特性

### 3. 多层次选择策略
- **第一阶段**：基于支配关系和MED距离的选择
- **第二阶段**：9种情况的环境选择策略，考虑ε约束可行性
- **第三阶段**：4种情况的精确选择，强化收敛性

### 4. 混合变异算子
- 结合SHADE自适应差分进化
- 多种DE变异策略：DE/transfer/1, DE/rand/1, DE/rand/2
- 自适应参数控制和多样性增强机制

## 📁 项目架构

```
Woven/
├── Woven.m                    # 主算法文件
├── Woven_Selection.m          # 选择策略实现
├── Woven_Mutation.m           # 变异算子实现
├── Woven_StageControl.m       # 阶段控制逻辑
├── Woven_Record.m             # 参数管理（单例模式）
├── Woven_Utility.m            # 工具函数集合
```


#### 九种选择情况处理
| 父代状态 | 子代状态 | 选择策略 |
|---------|---------|---------|
| 可行解 | 可行解 | 支配关系 + 多样性 |
| 可行解 | ε可行解 | 支配关系 + 多样性要求 |
| 可行解 | 不可行解 | 直接拒绝 |
| ε可行解 | 可行解 | 接受 + 多样性检查 |
| ε可行解 | ε可行解 | 支配关系 + 第一前沿支配次数 |
| ε可行解 | 不可行解 | 直接拒绝 |
| 不可行解 | 可行解 | 直接接受 |
| 不可行解 | ε可行解 | 直接接受 |
| 不可行解 | 不可行解 | 约束违反值比较 |


---

# 算法运行逻辑详解

## 算法整体执行逻辑

### 第一阶段选择逻辑

`Stage1Selection`函数的详细逻辑：

1. **支配关系检查**：
   ```matlab
   dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
   ```

2. **情况1：新解支配旧解** (`dom_result == 1`)
   ```matlab
   selected = true;  % 直接接受
   ```

3. **情况2：互不支配** (`dom_result == 0`)
   ```matlab
   % 计算当前种群非支配解
   non_dominated_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);
   
   % 计算被支配次数
   old_BeDomCT = Woven_Utility.calculateDominatedByNonDominatedSet(old_obj, PopObj, non_dominated_flags);
   
   % 计算新解的被支配次数（需要临时更新种群）
   temp_PopObj = PopObj;
   temp_PopObj(index, :) = new_obj;
   temp_non_dominated_flags = Woven_Utility.calculateStage1NonDominatedSet(temp_PopObj);
   new_BeDomCT = Woven_Utility.calculateDominatedByNonDominatedSet(new_obj, temp_PopObj, temp_non_dominated_flags);
   
   if new_BeDomCT < old_BeDomCT
       selected = true;  % 被支配次数更少
   elseif new_BeDomCT == old_BeDomCT
       % 被支配次数相同，比较MED距离
       old_med = Woven_Utility.calculateMEDDistance(old_obj, index, PopObj);
       new_med = Woven_Utility.calculateMEDDistance(new_obj, index, temp_PopObj);
       selected = (new_med > old_med);
   else
       selected = false;  % 被支配次数更多
   end
   ```

4. **情况3：旧解支配新解** (`dom_result == -1`)
   ```matlab
   selected = false;  % 直接拒绝
   ```

### 第一阶段变异逻辑

在`mutate(candidate, stage=1, fitness, Problem)`中：

1. **策略选择**（基于概率`stage1_normal_prob = 0.5`）：

   **策略A：GA高斯变异**（概率50%）
   ```matlab
   k = randi(D);  % 随机选择一个维度
   distance_to_boundary = min(candidate(k), 1 - candidate(k));
   sigma = min(0.5, distance_to_boundary * stage1_sigma_scale);
   temp = candidate(k) + randn * sigma;
   if temp >= 0 && temp <= 1
       offspring(k) = temp;
   end
   ```

   **策略B：DE/transfer/1变异**（概率50%）
   ```matlab
   if archive_size >= 2
       % 从存档中随机选择2个个体
       indices = randperm(population_size, 2);
       r1 = archive(indices(1), :);
       r2 = archive(indices(2), :);
       
       % 动态F值
       F = de_f_min + (de_f_max - de_f_min) * rand;
       
       % DE/transfer/1: v = candidate + F * (r1 - r2)
       v = candidate + F * (r1 - r2);
       offspring = max(0, min(1, v));  % 边界处理
   end
   ```

### 第一阶段转换条件检查

在每代结束后执行`checkStage1Transition`：

1. **最大评估次数检查**：
   ```matlab
   max_eval_count = Problem.FE >= Problem.maxFE * stage1_max_fe;
   if max_eval_count, transition = true; return; end
   ```

2. **最小评估次数检查**：
   ```matlab
   min_eval_count = Problem.FE >= Problem.maxFE * stage1_min_fe;
   if ~min_eval_count, transition = false; return; end
   ```

3. **解质量检查**：
   ```matlab
   % 计算非支配解比例
   obj_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);
   non_dominated_count = sum(obj_flags == 1);
   pareto_front_ratio = non_dominated_count / N;
   
   if pareto_front_ratio < first_front_ratio  % 通常为0.60
       transition = false; return;
   end
   
   % 计算二层解
   second_layer_flags = Woven_Utility.calculateSecondLayerSolutions(PopObj, obj_flags);
   combined_ratio = (non_dominated_count + sum(second_layer_flags)) / N;
   
   if combined_ratio < non_dominated_threshold  % 通常为0.93
       transition = false; return;
   end
   ```

4. **收敛性检查**：
   ```matlab
   % 计算目标空间变化
   current_change = Woven_Utility.calculateObjectiveChange(PopObj, previous_objectives);
   lambda = Woven_Utility.calculateAdaptiveThreshold(PopObj, M, N);
   
   if current_change <= lambda
       consecutive_nochange_count++;
   else
       consecutive_nochange_count = 0;
   end
   
   convergence_condition = consecutive_nochange_count >= consecutive_gen_threshold;
   transition = convergence_condition;
   ```

## 第二阶段运行逻辑

### 阶段目标
引入ε约束法，逐步收紧约束条件，实现从不可行域到可行域的平滑过渡。


#### ε值计算
每代开始时计算当前的ε约束值：
```matlab
stage2_elapsed_fe = Problem.FE - Algorithm.record.stage2_start_fe;
stage2_max_fe = Problem.maxFE * Algorithm.record.stage2_max_fe - Algorithm.record.stage2_start_fe;
epsilon_values = Woven_Utility.calculateEpsilonValues(PopCon, stage2_elapsed_fe, stage2_max_fe, Algorithm.record.constraint_beta);
```

#### 第一前沿解计算
```matlab
first_layer_flags = Woven_Utility.calculateStage23NonDominatedSet(PopObj, PopCon, Problem);
```

#### 个体级处理
对每个个体执行：

1. **生成子代**：
   ```matlab
   new_dec = Algorithm.mutation.mutate(old_dec, stage=2);
   offspring = Problem.Evaluation(new_dec);
   ```

2. **选择决策**：
   ```matlab
   selected = Algorithm.selection.Stage2Selection(old_dec, old_obj, old_con, new_dec, new_obj, new_con, i, PopDec, PopObj, PopCon, epsilon_values, first_layer_flags);
   ```

### 第二阶段选择逻辑

`Stage2Selection`执行九种情况的完整分类：

#### 可行性判断
```matlab
% 真实可行性
parent_feasible = sum(max(0, old_con)) <= 0;
child_feasible = sum(max(0, new_con)) <= 0;

% ε约束可行性
parent_epsilon_feasible = Woven_Utility.checkEpsilonFeasibility(old_con, epsilon_values);
child_epsilon_feasible = Woven_Utility.checkEpsilonFeasibility(new_con, epsilon_values);
```

### 第二阶段变异逻辑

```matlab
if rand < stage2_normal_prob  % 概率99%
    % 正态变异
    k = randi(D);
    temp = candidate(k) + randn * stage2_sigma;
    if temp >= 0 && temp <= 1
        offspring(k) = temp;
    end
else
    % DE/rand/1变异
    if archive_size >= 3
        indices = randperm(archive_size, 3);
        r1 = archive(indices(1), :);
        r2 = archive(indices(2), :);
        r3 = archive(indices(3), :);
        
        F = rand;
        v = r1 + F * (r2 - r3);
        offspring = max(0, min(1, v));
    end
end
```

### 第二阶段转换条件检查

1. **边界交织条件**：
   ```matlab
   interwoven_condition = obj.checkInterwovenBoundaries(X, V, Cons);
   ```

2. **几何分布检查**：
   ```matlab
   if interwoven_condition
       geometric_condition = obj.checkGeometricDistribution(X, V, Cons, Problem);
       interwoven_condition = interwoven_condition && geometric_condition;
   end
   ```

3. **最终转换决策**：
   ```matlab
   max_eval_count = Problem.FE >= Problem.maxFE * stage2_max_fe;
   transition = interwoven_condition || max_eval_count;
   ```

## 第三阶段运行逻辑

### 阶段目标
严格约束处理，精确收敛到约束Pareto前沿，不再使用ε约束。


### 第三阶段选择逻辑

基于严格约束可行性的四种情况：

#### 可行性判断
```matlab
parent_feasible = sum(max(0, old_con)) <= 0;
child_feasible = sum(max(0, new_con)) <= 0;
```

#### 四种选择情况

**情况1：可行解→可行解**
```matlab
dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
if dom_result == 1
    selected = true;  % 新解支配旧解
elseif dom_result == 0
    selected = obj.Stage3SelectionScore(...);  % 检查多样性
else
    selected = false;  % 旧解支配新解
end
```

**情况2：可行解→不可行解**
```matlab
selected = false;  % 直接拒绝
```

**情况3：不可行解→可行解**
```matlab
selected = true;  % 直接接受
```

**情况4：不可行解→不可行解**
```matlab
% 计算第三阶段进度
stage3_elapsed_fe = Problem.FE - obj.record.stage3_start_fe;
stage3_total_fe = Problem.maxFE - obj.record.stage3_start_fe;
stage3_progress = stage3_elapsed_fe / stage3_total_fe;

if stage3_progress <= 0.5
    % 前50%：考虑被支配次数
    old_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(old_obj, PopObj, first_layer_flags);
    new_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(new_obj, PopObj, first_layer_flags);
    
    if new_dominated_count <= old_dominated_count
        old_cv = sum(max(0, old_con));
        new_cv = sum(max(0, new_con));
        selected = (new_cv < old_cv);
    else
        selected = false;
    end
else
    % 后50%：强制收敛CV
    old_cv = sum(max(0, old_con));
    new_cv = sum(max(0, new_con));
    selected = (new_cv < old_cv);
end
```

### 第三阶段变异逻辑

```matlab
if rand < stage3_normal_prob  % 概率99%
    % 正态变异
    k = randi(D);
    temp = candidate(k) + randn * stage3_sigma;
    if temp >= 0 && temp <= 1
        offspring(k) = temp;
    end
else
    % DE/rand/2变异（如果存档个体足够）
    if archive_size >= 5
        indices = randperm(archive_size, 5);
        r1 = archive(indices(1), :);
        r2 = archive(indices(2), :);
        r3 = archive(indices(3), :);
        r4 = archive(indices(4), :);
        r5 = archive(indices(5), :);
        
        F = de_f_min + (de_f_max - de_f_min) * rand;
        v = r1 + F * (r2 - r3) + F * (r4 - r5);
        offspring = max(0, min(1, v));
    else
        % 退化为DE/rand/1或其他策略
    end
end
```

