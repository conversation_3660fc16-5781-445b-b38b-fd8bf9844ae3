% 测试新的Stage2Selection实现
% 验证5类解分类系统和25种转换情况

clear; clc;

% 创建测试数据
N = 20; % 种群大小
M = 2;  % 目标维度
C = 2;  % 约束个数

% 生成测试种群
PopDec = rand(N, 5);  % 5维决策变量
PopObj = rand(N, M) * 10;  % 目标函数值
PopCon = (rand(N, C) - 0.7) * 2;  % 约束违反值，部分可行部分不可行

% 设置ε约束值
epsilon_values = [0.5, 0.3];

% 计算第一前沿标记
first_layer_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);

% 创建记录对象和选择对象
record = Woven_Record();
selector = Woven_Selection(record);

% 测试解分类功能
fprintf('=== 测试解分类功能 ===\n');
solution_types = Woven_Utility.classifyStage2Solutions(PopObj, PopCon, epsilon_values);

% 统计各类解的数量
type_counts = zeros(5, 1);
for i = 1:5
    type_counts(i) = sum(solution_types == i);
end

fprintf('解类型分布：\n');
fprintf('类型1（第一前沿可行解）: %d个\n', type_counts(1));
fprintf('类型2（非第一前沿可行解）: %d个\n', type_counts(2));
fprintf('类型3（支配第一前沿的ε不可行解）: %d个\n', type_counts(3));
fprintf('类型4（被第一前沿支配的ε不可行解）: %d个\n', type_counts(4));
fprintf('类型5（完全不可行解）: %d个\n', type_counts(5));

% 测试各种转换情况
fprintf('\n=== 测试转换情况 ===\n');

test_cases = [
    1, 1; 1, 2; 1, 3; 1, 4; 1, 5;
    2, 1; 2, 2; 2, 3; 2, 4; 2, 5;
    3, 1; 3, 2; 3, 3; 3, 4; 3, 5;
    4, 1; 4, 2; 4, 3; 4, 4; 4, 5;
    5, 1; 5, 2; 5, 3; 5, 4; 5, 5
];

success_count = 0;
total_tests = size(test_cases, 1);

for i = 1:total_tests
    old_type = test_cases(i, 1);
    new_type = test_cases(i, 2);
    
    try
        % 找到对应类型的解作为测试数据
        old_indices = find(solution_types == old_type);
        new_indices = find(solution_types == new_type);
        
        if ~isempty(old_indices) && ~isempty(new_indices)
            old_idx = old_indices(1);
            new_idx = new_indices(1);
            
            % 执行选择测试
            selected = selector.Stage2Selection(...
                PopDec(old_idx, :), PopObj(old_idx, :), PopCon(old_idx, :), ...
                PopDec(new_idx, :), PopObj(new_idx, :), PopCon(new_idx, :), ...
                old_idx, PopDec, PopObj, PopCon, epsilon_values, first_layer_flags);
            
            fprintf('转换 %d→%d: %s\n', old_type, new_type, ...
                    iif(selected, '接受新解', '保持旧解'));
            success_count = success_count + 1;
        else
            fprintf('转换 %d→%d: 跳过（缺少对应类型的解）\n', old_type, new_type);
        end
    catch ME
        fprintf('转换 %d→%d: 错误 - %s\n', old_type, new_type, ME.message);
    end
end

fprintf('\n=== 测试结果 ===\n');
fprintf('成功测试: %d/%d\n', success_count, total_tests);
fprintf('测试完成率: %.1f%%\n', success_count/total_tests*100);

% 辅助函数
function result = iif(condition, true_value, false_value)
    if condition
        result = true_value;
    else
        result = false_value;
    end
end
