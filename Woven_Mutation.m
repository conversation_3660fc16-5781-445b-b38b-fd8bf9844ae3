classdef Woven_Mutation < handle
    properties
        record;  % Woven_Record 对象，用于管理参数
    end

    methods
        % 构造函数，初始化记录对象
        function obj = Woven_Mutation(record)
            obj.record = record;
        end

        function offspring = mutate(obj, candidate, stage, fitness, Problem, PopDec, PopObj, PopCon, current_index)
            % 新的变异函数接口
            % candidate: 当前个体的决策变量
            % stage: 当前阶段 (1, 2, 3)
            % fitness: 当前个体的适应度值（目标函数值）
            % Problem: 问题对象
            % PopDec: 完整种群的决策变量矩阵
            % PopObj: 完整种群的目标函数值矩阵
            % PopCon: 完整种群的约束违反值矩阵
            % current_index: 当前个体在种群中的索引

            [~, D] = size(candidate);
            offspring = candidate;

            if stage == 1
                if rand < obj.record.stage1_normal_prob
                    % 高斯变异 - 使用重采样策略
                    offspring = obj.gaussianMutationWithResampling(candidate, 0.5);
                else
                    % DE/rand/1变异
                    offspring = obj.DE_rand1(candidate, PopDec, current_index);
                end
                
            elseif stage == 2
                if rand < obj.record.stage2_normal_prob
                    % 正态变异 - 使用重采样策略
                    offspring = obj.gaussianMutationWithResampling(candidate, obj.record.stage2_sigma);
                else
                    % DE/rand/1变异
                    offspring = obj.DE_rand1(candidate, PopDec, current_index);
                    % DE/rand/2变异（暂时注释）
                    % offspring = obj.DE_rand2(candidate, PopDec, current_index);
                end

            elseif stage == 3
                if rand < obj.record.stage3_normal_prob
                    % 正态变异 - 使用重采样策略
                    offspring = obj.gaussianMutationWithResampling(candidate, obj.record.stage3_sigma);
                else
                    % DE/rand/1变异
                    offspring = obj.DE_rand1(candidate, PopDec, current_index);
                    % DE/rand/2变异（暂时注释）
                    % offspring = obj.DE_rand2(candidate, PopDec, current_index);
                end
            end
        end
        
        function offspring = gaussianMutationWithResampling(obj, candidate, sigma)
            % 使用重采样策略的高斯变异
            % candidate: 当前个体的决策变量
            % sigma: 高斯扰动的标准差
            % 返回: offspring - 变异后的个体
            
            [~, D] = size(candidate);
            offspring = candidate;
            
            % 随机选择一个维度进行变异
            k = randi(D);
            
            % 使用repeat...until循环确保变异值在边界内
            max_attempts = 100; % 防止无限循环的最大尝试次数
            attempt = 0;
            temp = candidate(k); % 初始化为原值
            
            % repeat...until循环实现
            while true
                attempt = attempt + 1;
                
                % 生成高斯扰动
                temp = candidate(k) + randn * sigma;
                
                % 检查边界约束 - 如果满足条件则跳出循环
                if (temp >= 0) && (temp <= 1)
                    break;
                end
                
                % 防止无限循环的安全机制
                if attempt >= max_attempts
                    % 使用边界修复策略作为后备方案
                    if temp < 0
                        temp = 0;
                    elseif temp > 1
                        temp = 1;
                    end
                    break;
                end
            end
            
            % 更新变异后的值
            offspring(k) = temp;
        end
        
        function offspring = DE_rand1(obj, candidate, PopDec, current_index)
            % DE/rand/1变异策略
            % 公式: v = r1 + F * (r2 - r3)
            % 需要3个随机个体
            
            [N, D] = size(PopDec);
            offspring = candidate;
            
            if N >= 4  % 需要至少4个个体（3个用于变异 + 1个当前个体）
                % 排除当前个体，从其他个体中随机选择3个
                other_indices = setdiff(1:N, current_index);
                if length(other_indices) >= 3
                    selected = other_indices(randperm(length(other_indices), 3));
                    r1 = PopDec(selected(1), :);  % 基向量
                    r2 = PopDec(selected(2), :);  % 差分向量1
                    r3 = PopDec(selected(3), :);  % 差分向量2
                    
                    % DE/rand/1公式
                    F = rand; % 随机缩放因子 [0,1]
                    v = r1 + F * (r2 - r3);
                    
                    % 边界处理：逐个检查每个维度
                    for j = 1:D
                        if v(j) >= 0 && v(j) <= 1
                            offspring(j) = v(j);
                        end
                    end
                end
            end
        end
        
        function offspring = DE_rand2(obj, candidate, PopDec, current_index)
            % DE/rand/2变异策略
            % 公式: v = r1 + F * (r2 - r3) + F * (r4 - r5)
            % 需要5个随机个体
            
            [N, D] = size(PopDec);
            offspring = candidate;
            
            if N >= 6  % 需要至少6个个体（5个用于变异 + 1个当前个体）
                % 排除当前个体，从其他个体中随机选择5个
                other_indices = setdiff(1:N, current_index);
                if length(other_indices) >= 5
                    selected = other_indices(randperm(length(other_indices), 5));
                    r1 = PopDec(selected(1), :);  % 基向量
                    r2 = PopDec(selected(2), :);  % 差分向量1-1
                    r3 = PopDec(selected(3), :);  % 差分向量1-2
                    r4 = PopDec(selected(4), :);  % 差分向量2-1
                    r5 = PopDec(selected(5), :);  % 差分向量2-2
                    
                    % DE/rand/2公式
                    F = rand; % 随机缩放因子 [0,1]
                    v = r1 + F * (r2 - r3) + F * (r4 - r5);
                    
                    % 边界处理：逐个检查每个维度
                    for j = 1:D
                        if v(j) >= 0 && v(j) <= 1
                            offspring(j) = v(j);
                        end
                    end
                end
            end
            % 如果种群不足或其他条件不满足，返回原始个体
        end
    end
end