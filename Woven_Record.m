classdef Woven_Record < handle    
    properties
        % SHADE基础参数
        stage2_start_fe;      % 阶段2开始评估次数
        stage3_start_fe;      % 阶段3开始评估次数
        
        % 变异阶段相关参数
        stage1_normal_prob = 0.3;    % 阶段1正态变异概率
        stage2_normal_prob = 0.99;   % 阶段2正态变异概率
        stage3_normal_prob = 0.99;   % 阶段3正态变异概率

        % 扰动相关参数
        stage1_sigma = 1.2;    % 阶段1高斯扰动幅度比例（增大）
        stage2_sigma = 0.1;          % 阶段2高斯扰动幅度
        stage3_sigma = 0.1;          % 阶段3高斯扰动幅度
        
        % StageControl相关参数
        stage1_min_fe = 0.25;          % 阶段1最小评估比例
        stage1_max_fe = 0.40;           % 阶段1最大评估比例
        stage2_max_fe = 0.75;          % 阶段2最大评估比例
        non_dominated_threshold = 0.80;  % 一二级非支配解阈值
        first_front_ratio = 0.60;      % 第一层非支配解阈值
        interwoven_threshold = 0.6;     % 边界交织阈值   要求的可行解比例是多少
        consecutive_gen_threshold = 2;  % 连续N代无显著变化的阈值
        
        % 选择因子权重 - 阶段2
        stage2_factor_weights = struct(...
            'decision_space_diversity', 0.10, ...
            'angle', 0.30, ...
            'med', 0.60 ...
        );
        
        % 选择因子权重 - 阶段3
        stage3_factor_weights = struct(...
            'angle', 0.30, ...
            'med', 0.60, ...
            'diversity', 0.10 ...
        );
        constraint_beta = 0.15;     % 约束收缩指数
    end
    

    
    methods (Static)
        function obj = getInstance()
            persistent localInstance   % 使用persistent变量实现单例
            if isempty(localInstance) || ~isvalid(localInstance)
                localInstance = Woven_Record();
            end
            obj = localInstance;
        end
    end

    methods (Access = private)
        function obj = Woven_Record()
            % 私有构造函数，确保单例模式
        end
    end
end
