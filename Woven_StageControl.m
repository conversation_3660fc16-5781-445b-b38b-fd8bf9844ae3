classdef Woven_StageControl < handle
    % Woven_StageControl 控制三阶段约束多目标进化算法的转换逻辑和收敛性计算
    
    properties
        previous_objectives = []; % 上一代目标值（第一阶段）
        problem;
        consecutive_nochange_count = 0;  % 连续无改进计数（第一阶段）
        record;                          % 添加record属性
    end
    
    methods
        % 添加构造函数
        function obj = Woven_StageControl(record)
            obj.record = record;
        end
        
        function setProblem(obj, problem)
            obj.problem = problem;
        end
        
        function transition = checkStage1Transition(obj, Problem, PopObj, PopCon, obj_flags)
            max_eval_count = Problem.FE >= Problem.maxFE * obj.record.stage1_max_fe;
            if max_eval_count
                transition = true;
                return;
            end
            
            min_eval_count = Problem.FE >= Problem.maxFE * obj.record.stage1_min_fe;
            if ~min_eval_count
                transition = false;
                return;
            end
            
            [N, M] = size(PopObj);
            non_dominated_count = sum(obj_flags == 1);
            second_layer_flags = Woven_Utility.calculateSecondLayerSolutions(PopObj, obj_flags);
            second_layer_count = sum(second_layer_flags == 1);

            pareto_front_ratio = non_dominated_count / N;
            if pareto_front_ratio < obj.record.first_front_ratio
                transition = false;
                return;
            end
            combined_ratio = (non_dominated_count + second_layer_count) / N;
            combined_condition = combined_ratio >= obj.record.non_dominated_threshold;
            
            if ~combined_condition
                obj.previous_objectives = PopObj;
                transition = false;
                return;
            end
            
            current_change = Woven_Utility.calculateObjectiveChange(PopObj, obj.previous_objectives);
            lambda = Woven_Utility.calculateAdaptiveThreshold(PopObj, M, N);
            
            if current_change <= lambda
                obj.consecutive_nochange_count = obj.consecutive_nochange_count + 1;
            else
                obj.consecutive_nochange_count = 0;
            end
            
            obj.previous_objectives = PopObj;
            convergence_condition = obj.consecutive_nochange_count >= obj.record.consecutive_gen_threshold;
            
            transition = convergence_condition;
        end
        
        function transition = checkStage2Transition(obj, Problem, X, V, Cons)
            interwoven_condition = obj.checkInterwovenBoundaries(X, V, Cons); % 边界交织条件

            % 添加几何分布检查
            if interwoven_condition
                geometric_condition = obj.checkGeometricDistribution(X, V, Cons, Problem);
                interwoven_condition = interwoven_condition && geometric_condition;
            end

            max_eval_count = Problem.FE >= Problem.maxFE * obj.record.stage2_max_fe; % 保底条件
            transition = interwoven_condition || max_eval_count;
        end

        function early_stop = checkStage2EarlyStop(obj, Problem, PopObj, PopCon)
            % 第二阶段早停机制 - 简化版本
            % 检查条件：
            % 1. 第一前沿比例 >= 阈值
            % 2. 第一+第二前沿组合比例 >= 阈值  
            % 3. 不可行解比例 < 10%

            % 计算第一前沿解（考虑约束）
            obj_flags = Woven_Utility.calculateStage23NonDominatedSet(PopObj, PopCon, Problem);

            [N, M] = size(PopObj);
            
            % 条件1: 检查第一前沿比例
            non_dominated_count = sum(obj_flags == 1);
            pareto_front_ratio = non_dominated_count / N;
            if pareto_front_ratio < obj.record.first_front_ratio
                early_stop = false;
                return;
            end
            
            % 条件2: 检查第一+第二前沿组合比例
            second_layer_flags = Woven_Utility.calculateSecondLayerSolutions(PopObj, obj_flags);
            second_layer_count = sum(second_layer_flags == 1);
            combined_ratio = (non_dominated_count + second_layer_count) / N;
            if combined_ratio < obj.record.non_dominated_threshold
                early_stop = false;
                return;
            end
            
            % 条件3: 检查不可行解比例是否低于10%
            feasible = sum(max(0, PopCon), 2) <= 0;
            feasible_count = sum(feasible);
            infeasible_ratio = (N - feasible_count) / N;
            if infeasible_ratio >= 0.10  % 不可行解比例 >= 10%
                early_stop = false;
                return;
            end
            
            % 所有条件都满足，触发早停
            early_stop = true;
        end

        function early_stop = checkStage3EarlyStop(obj, Problem, PopObj, PopCon)
            % 第三阶段早停机制
            % 检查条件：
            % 1. 第一前沿比例 >= 阈值
            % 2. 第一+第二前沿组合比例 >= 阈值
            % 3. 不可行解比例为0

            % 计算第一前沿解（考虑约束）
            obj_flags = Woven_Utility.calculateStage23NonDominatedSet(PopObj, PopCon, Problem);

            [N, M] = size(PopObj);
            
            % 条件1: 检查第一前沿比例
            non_dominated_count = sum(obj_flags == 1);
            pareto_front_ratio = non_dominated_count / N;
            if pareto_front_ratio < obj.record.first_front_ratio
                early_stop = false;
                return;
            end
            
            % 条件2: 检查第一+第二前沿组合比例
            second_layer_flags = Woven_Utility.calculateSecondLayerSolutions(PopObj, obj_flags);
            second_layer_count = sum(second_layer_flags == 1);
            combined_ratio = (non_dominated_count + second_layer_count) / N;
            if combined_ratio < obj.record.non_dominated_threshold
                early_stop = false;
                return;
            end
            
            % 条件3: 检查不可行解比例是否为0
            feasible = sum(max(0, PopCon), 2) <= 0;
            feasible_count = sum(feasible);
            infeasible_ratio = (N - feasible_count) / N;
            if infeasible_ratio > 0  % 不可行解比例 > 0
                early_stop = false;
                return;
            end
            
            % 所有条件都满足，触发早停
            early_stop = true;
        end
        
        function result = checkInterwovenBoundaries(obj, X, V, Cons)
            [N, M] = size(V);
            feasible = sum(max(0, Cons), 2) <= 0;
            feasible_ratio = sum(feasible) / N;
            
            if feasible_ratio < 0.1
                result = false;
                return;
            elseif feasible_ratio >= 0.9
                result = true;
                return;
            end
            
            feasible_obj = V(feasible,:);  % 获取可行解和不可行解
            infeasible_obj = V(~feasible,:);
            if isempty(feasible_obj) || isempty(infeasible_obj)% 可行解或不可行解为空
                result = false;
                return;
            end
            overlap = zeros(1, M); % 计算边界重叠情况
            total_range = zeros(1, M);
            for i = 1:M
                feasible_min = min(feasible_obj(:,i));
                feasible_max = max(feasible_obj(:,i));
                infeasible_min = min(infeasible_obj(:,i));
                infeasible_max = max(infeasible_obj(:,i));
                overlap(i) = max(0, min(feasible_max, infeasible_max) - max(feasible_min, infeasible_min));% 计算重叠
                total_range(i) = max(feasible_max, infeasible_max) - min(feasible_min, infeasible_min);% 计算总范围
            end
            interwoven_ratio = overlap ./ (total_range + eps);
            result = all(interwoven_ratio >= obj.record.interwoven_threshold);
        end
        
        function result = checkGeometricDistribution(obj, X, V, Cons, Problem)
            % 检查考虑约束的非支配解的几何分布质量
            % 寻找是否存在5个点的子集，使得相邻点夹角都大于10度
            
            % 获取考虑约束的非支配解
            obj_flags = Woven_Utility.calculateStage23NonDominatedSet(V, Cons, Problem);
            non_dominated_indices = find(obj_flags == 1);
            
            % 如果非支配解少于5个，直接返回false
            if length(non_dominated_indices) < 5
                result = false;
                return;
            end
            
            % 获取非支配解的目标值
            non_dominated_obj = V(non_dominated_indices, :);
            n = size(non_dominated_obj, 1);
            
            % 检查所有5个点的组合
            for i = 1:n-4
                for j = i+1:n-3
                    for k = j+1:n-2
                        for l = k+1:n-1
                            for m = l+1:n
                                % 获取5个点
                                points = [non_dominated_obj(i,:); non_dominated_obj(j,:); 
                                         non_dominated_obj(k,:); non_dominated_obj(l,:); 
                                         non_dominated_obj(m,:)];
                                
                                % 检查是否存在满足条件的排列
                                if obj.checkValidArrangement(points)
                                    result = true;
                                    return;
                                end
                            end
                        end
                    end
                end
            end
            
            result = false;
        end
        
        function valid = checkValidArrangement(obj, points)
            % 检查5个点是否存在满足相邻夹角>10度的排列
            % points: 5x M 矩阵，5个点的目标值
            
            n = size(points, 1);
            indices = 1:n;
            
            % 生成所有排列 (自定义实现替代perms函数)
            all_perms = obj.generatePermutations(indices);
            
            for i = 1:size(all_perms, 1)
                perm = all_perms(i, :);
                arranged_points = points(perm, :);
                
                % 检查这个排列是否满足相邻夹角条件
                if obj.checkAdjacentAngles(arranged_points)
                    valid = true;
                    return;
                end
            end
            
            valid = false;
        end
        
        function perms_matrix = generatePermutations(obj, vec)
            % 自定义排列生成函数，替代MATLAB的perms函数
            % vec: 输入向量
            % perms_matrix: 所有排列的矩阵
            
            n = length(vec);
            if n == 0
                perms_matrix = [];
                return;
            elseif n == 1
                perms_matrix = vec;
                return;
            end
            
            % 计算排列总数
            total_perms = factorial(n);
            perms_matrix = zeros(total_perms, n);
            
            % 递归生成所有排列
            perms_cell = obj.permuteRecursive(vec);
            
            % 将cell array转换为矩阵
            for i = 1:length(perms_cell)
                perms_matrix(i, :) = perms_cell{i};
            end
        end
        
        function perms_cell = permuteRecursive(obj, vec)
            % 递归生成排列的辅助函数
            n = length(vec);
            if n == 1
                perms_cell = {vec};
                return;
            end
            
            perms_cell = {};
            for i = 1:n
                % 取出第i个元素
                current_element = vec(i);
                % 剩余元素
                remaining = [vec(1:i-1), vec(i+1:end)];
                
                % 递归生成剩余元素的排列
                sub_perms = obj.permuteRecursive(remaining);
                
                % 将当前元素添加到每个子排列的前面
                for j = 1:length(sub_perms)
                    perms_cell{end+1} = [current_element, sub_perms{j}];
                end
            end
        end
        
        function valid = checkAdjacentAngles(obj, arranged_points)
            % 检查排列后的点是否满足相邻夹角>10度
            % arranged_points: 5x M 矩阵，按顺序排列的5个点
            
            angle_threshold = 10; % 10度
            angle_threshold_rad = angle_threshold * pi / 180; % 转换为弧度
            
            for i = 1:4 % 检查4个相邻对
                p1 = arranged_points(i, :);
                p2 = arranged_points(i+1, :);
                
                % 计算两个向量的夹角（以原点为参考）
                dot_product = dot(p1, p2);
                norm_p1 = norm(p1);
                norm_p2 = norm(p2);
                
                if norm_p1 == 0 || norm_p2 == 0
                    valid = false;
                    return;
                end
                
                cos_angle = dot_product / (norm_p1 * norm_p2);
                cos_angle = max(-1, min(1, cos_angle)); % 确保在[-1,1]范围内
                angle = acos(cos_angle);
                
                if angle <= angle_threshold_rad
                    valid = false;
                    return;
                end
            end
            
            valid = true;
        end
        function result = checkInterwoven(~, obj_flags, PopObj, PopCon)
            % 检查边界交织情况
            [N, M] = size(PopObj);
            feasible = sum(max(0, PopCon), 2) <= 0;
            feasible_ratio = sum(feasible) / N;
            
            % 如果可行解比例超过90%，直接返回true
            if (feasible_ratio >= 0.9)
                result = true;
                return;
            end
            
            % 如果可行解比例低于10%，直接返回false
            if (feasible_ratio < 0.1)
                result = false;
                return;
            end
            
            % 获取可行解和不可行解
            feasible_obj = PopObj(feasible,:);
            infeasible_obj = PopObj(~feasible,:);
            
            % 可行解或不可行解为空
            if isempty(feasible_obj) || isempty(infeasible_obj)
                result = false;
                return;
            end    
            % 计算网格大小
            obj_min = min(PopObj);
            obj_max = max(PopObj);
            obj_range = mean(obj_max - obj_min);
            grid_size = max(0.1, min(0.05, obj_range * 0.02));
            
            % 对每个可行非支配解检查其周围的不可行点比例
            non_dominated_indices = find(obj_flags == 1);
            total_points = 0;
            infeasible_points = 0;
            
            for idx = non_dominated_indices'
                center = PopObj(idx,:);
                
                % 计算所有点到该中心的距离
                distances = sqrt(sum((PopObj - repmat(center, N, 1)).^2, 2));
                
                % 找出网格内的点
                points_in_grid = distances <= grid_size;
                
                % 统计总点数和不可行点数
                grid_total = sum(points_in_grid) - 1;  % 减1是为了排除自身
                if grid_total > 0
                    total_points = total_points + grid_total;
                    infeasible_points = infeasible_points + sum(points_in_grid & ~feasible) - (idx > 0 && ~feasible(idx));
                end
            end
            min_total_count = max(15, round(N * 0.05));
            if total_points < min_total_count
                result = false;
                return;
            end
            infeasible_ratio = infeasible_points / total_points;
            ratio_threshold = 0.3; 
            result = infeasible_ratio >= ratio_threshold;
        end
    end  
end
