classdef Woven < ALGORITHM
    % <multi/many> <real/integer/label/binary/permutation> <constrained/large/expensivie/multimodal/sparse/dynamic/multitask/bilevel/robust>
    % Woven - 约束多目标进化策略
    % 三阶段约束多目标进化策略
    
    properties
        stage = 1;          % Current stage (当前阶段)
        obj_flags = [];     % Objective flags (目标标志)
        mutation;           % Mutation operator
        selection;          % Selection operator
        stageControl;      % Stage control
        record;            % Record instance
    end
    methods
        function main(Algorithm,Problem)
            Algorithm.record = Woven_Record.getInstance();
            
            Algorithm.mutation = Woven_Mutation(Algorithm.record);
            Algorithm.selection = Woven_Selection(Algorithm.record);
            Algorithm.stageControl = Woven_StageControl(Algorithm.record);
            
            % 初始化种群
            Population = Problem.Initialization();
            PopDec = Population.decs;
            PopObj = Population.objs;
            PopCon = Population.cons;
            disp('当前运行版本为大改版本v1')
            while Algorithm.NotTerminated(Population)
                switch Algorithm.stage
                    case 1
                        [PopDec, PopObj, PopCon] = Algorithm.firstStage(Problem, PopDec, PopObj, PopCon);
                        Algorithm.obj_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);
                        if Algorithm.stageControl.checkStage1Transition(Problem, PopObj, PopCon, Algorithm.obj_flags)
                            fprintf('=== 进入第二阶段 ===\n');
                            fprintf('当前评估次数: %d / %d\n', Problem.FE, Problem.maxFE);
                            Algorithm.record.stage2_start_fe = Problem.FE;
                            Algorithm.stage = 2;
                        end
                    case 2
                        [PopDec, PopObj, PopCon] = Algorithm.secondStage(Problem, PopDec, PopObj, PopCon);
                        %if Algorithm.stageControl.checkStage2Transition(Problem, PopDec, PopObj, PopCon)
                        %    fprintf('=== 进入第三阶段 ===\n');
                        %    fprintf('当前评估次数: %d / %d\n', Problem.FE, Problem.maxFE);
                        %    Algorithm.record.stage3_start_fe = Problem.FE;
                        %    Algorithm.stage = 3;
                        %end
                    case 3
                        [PopDec, PopObj, PopCon] = Algorithm.thirdStage(Problem, PopDec, PopObj, PopCon); 
                end
                Population = SOLUTION(PopDec, PopObj, PopCon);
            end
        end
        
        function [PopDec, PopObj, PopCon] = firstStage(Algorithm, Problem, PopDec, PopObj, PopCon)
            N = size(PopDec,1);
            
            % 在循环开始前计算非支配解集合（对应CMOES的NonDominateSetCal）
            Algorithm.obj_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);
            
            for i = 1:N
                old_dec = PopDec(i,:);
                old_obj = PopObj(i,:);
                old_con = PopCon(i,:);
                
                % 变异操作
                new_dec = Algorithm.mutation.mutate(old_dec, 1, old_obj, Problem, PopDec, PopObj, PopCon, i);
                
                % 评估新个体
                offspring = Problem.Evaluation(new_dec);
                new_obj = offspring.objs;
                new_con = offspring.cons;
                
                % 修改选择逻辑：完全按照CMOES第一阶段逻辑
                % Step 1: 检查支配关系
                dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
                selected = false;
                
                if dom_result == 1
                    % 情况1：新个体支配旧个体 - 直接接受
                    selected = true;
                    
                elseif dom_result == -1
                    % 情况2：旧个体支配新个体 - 保持旧个体
                    selected = false;
                    
                else
                    % 情况3：相互非支配 - 使用被支配计数和MED距离
                    
                    % 计算被支配数量
                    old_dominated_count = 0;
                    new_dominated_count = 0;
                    
                    % 计算old_obj被支配次数
                    for j = 1:N
                        if j == i
                            continue; % 跳过自身
                        end
                        dom_check = Woven_Utility.checkDominance(PopObj(j,:), old_obj);
                        if dom_check == 1
                            old_dominated_count = old_dominated_count + 1;
                        end
                    end
                    
                    % 计算new_obj被支配次数
                    for j = 1:N
                        if j == i
                            continue; % 跳过自身
                        end
                        dom_check = Woven_Utility.checkDominance(PopObj(j,:), new_obj);
                        if dom_check == 1
                            new_dominated_count = new_dominated_count + 1;
                        end
                    end
                    
                    if new_dominated_count < old_dominated_count
                        % 新个体被支配数量更少，接受新个体
                        selected = true;
                        
                    elseif new_dominated_count == old_dominated_count
                        old_med = Woven_Utility.calculateMEDDistance(old_obj, i, PopObj);
                        new_med = Woven_Utility.calculateMEDDistance(new_obj, i, PopObj);
                        
                        selected = (new_med > old_med);
                        
                    else
                        % 新个体被支配数量更多，保持旧个体
                        selected = false;
                    end
                end
                
                % 更新种群
                if selected
                    PopDec(i,:) = new_dec;
                    PopObj(i,:) = new_obj;
                    PopCon(i,:) = new_con;
                end
            end
        end
        function [PopDec, PopObj, PopCon] = secondStage(Algorithm, Problem, PopDec, PopObj, PopCon)
            % 计算ε约束值
            stage2_elapsed_fe = Problem.FE - Algorithm.record.stage2_start_fe;
            stage2_max_fe = Problem.maxFE * Algorithm.record.stage2_max_fe;
            epsilon_values = Woven_Utility.calculateEpsilonValues(PopCon, stage2_elapsed_fe, stage2_max_fe, Algorithm.record.constraint_beta);
            
            N = size(PopDec, 1);
            
            % 计算考虑约束的第一前沿解
            first_layer_flags = Woven_Utility.calculateStage23NonDominatedSet(PopObj, PopCon, Problem);
            
            for i = 1:N
                old_dec = PopDec(i,:);
                old_obj = PopObj(i,:);
                old_con = PopCon(i,:);
                
                new_dec = Algorithm.mutation.mutate(old_dec, 2, old_obj, Problem, PopDec, PopObj, PopCon, i);
                
                offspring = Problem.Evaluation(new_dec);
                new_obj = offspring.objs;
                new_con = offspring.cons;
                
                selected = Algorithm.selection.Stage2Selection(old_dec, old_obj, old_con, new_dec, new_obj, new_con, i, PopDec, PopObj, PopCon, epsilon_values, first_layer_flags);
                
                if selected
                    PopDec(i,:) = new_dec;
                    PopObj(i,:) = new_obj;
                    PopCon(i,:) = new_con;
                end
            end
        end

        function [PopDec, PopObj, PopCon] = thirdStage(Algorithm, Problem, PopDec, PopObj, PopCon)
            epsilon_values = zeros(1, size(PopCon, 2)); % 第三阶段使用严格约束（epsilon=0）
            
            N = size(PopDec, 1);
            
            % 计算考虑约束的第一前沿解
            first_layer_flags = Woven_Utility.calculateStage23NonDominatedSet(PopObj, PopCon, Problem);
            
            for i = 1:N
                old_dec = PopDec(i,:);
                old_obj = PopObj(i,:);
                old_con = PopCon(i,:);
                
                new_dec = Algorithm.mutation.mutate(old_dec, 3, old_obj, Problem, PopDec, PopObj, PopCon, i);
                
                offspring = Problem.Evaluation(new_dec);
                new_obj = offspring.objs;
                new_con = offspring.cons;
                selected = Algorithm.selection.Stage3Selection(old_dec, old_obj, old_con, new_dec, new_obj, new_con, i, PopDec, PopObj, PopCon, first_layer_flags, Problem);
                
                if selected
                    PopDec(i,:) = new_dec;
                    PopObj(i,:) = new_obj;
                    PopCon(i,:) = new_con;
                end
            end
        end
    end
end